import { useState, useCallback } from 'react';

/**
 * Hook personalizzato per gestire il menu contestuale
 * 
 * @returns {Object} Oggetto con stato e funzioni per gestire il menu contestuale
 */
const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState({
    open: false,
    anchorPosition: null,
    contextData: null
  });

  // Apre il menu contestuale
  const openContextMenu = useCallback((event, data = null) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      open: true,
      anchorPosition: {
        top: event.clientY,
        left: event.clientX
      },
      contextData: data
    });
  }, []);

  // Chiude il menu contestuale
  const closeContextMenu = useCallback(() => {
    setContextMenu({
      open: false,
      anchorPosition: null,
      contextData: null
    });
  }, []);

  // Gestisce il click destro
  const handleContextMenu = useCallback((event, data = null) => {
    openContextMenu(event, data);
  }, [openContextMenu]);

  return {
    contextMenu,
    openContextMenu,
    closeContextMenu,
    handleContextMenu
  };
};

export default useContextMenu;
