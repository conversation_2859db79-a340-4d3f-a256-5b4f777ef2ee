import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  Grid
} from '@mui/material';
import {
  Straighten as RulerIcon,
  Cable as CableIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

/**
 * Dialogo per inserire i metri posati di un cavo
 * 
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Se il dialogo è aperto
 * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude
 * @param {Object} props.cavo - Dati del cavo selezionato
 * @param {Function} props.onSave - Funzione chiamata quando si salvano i metri
 * @param {boolean} props.loading - Indica se il salvataggio è in corso
 */
const InserisciMetriDialog = ({
  open = false,
  onClose = () => {},
  cavo = null,
  onSave = () => {},
  loading = false
}) => {
  const [metriPosati, setMetriPosati] = useState('');
  const [error, setError] = useState('');

  // Reset quando si apre il dialogo
  useEffect(() => {
    if (open && cavo) {
      setMetriPosati(cavo.metratura_reale?.toString() || '');
      setError('');
    }
  }, [open, cavo]);

  const handleSave = () => {
    // Validazione
    const metri = parseFloat(metriPosati);
    
    if (isNaN(metri) || metri < 0) {
      setError('Inserire un valore numerico valido maggiore o uguale a 0');
      return;
    }

    if (metri > cavo.metri_teorici * 1.1) {
      setError(`I metri posati non possono superare significativamente i metri teorici (${cavo.metri_teorici}m)`);
      return;
    }

    setError('');
    onSave(cavo.id_cavo, metri);
  };

  const handleClose = () => {
    if (!loading) {
      setError('');
      onClose();
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !loading && metriPosati.trim()) {
      handleSave();
    }
  };

  if (!cavo) return null;

  const progressPercentage = cavo.metri_teorici > 0 
    ? Math.min((parseFloat(metriPosati) || 0) / cavo.metri_teorici * 100, 100)
    : 0;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <RulerIcon color="primary" />
          <Typography variant="h6">
            Inserisci Metri Posati
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          {/* Informazioni cavo */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12}>
              <Box sx={{ 
                p: 2, 
                backgroundColor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <CableIcon fontSize="small" color="primary" />
                  <Typography variant="subtitle1" fontWeight="bold">
                    {cavo.id_cavo}
                  </Typography>
                  <Chip 
                    label={cavo.stato_installazione} 
                    size="small" 
                    color={cavo.stato_installazione === 'IN_CORSO' ? 'warning' : 'error'}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  <strong>Tipologia:</strong> {cavo.tipologia} | <strong>Sezione:</strong> {cavo.sezione}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Metri teorici:</strong> {cavo.metri_teorici}m | <strong>Attualmente posati:</strong> {cavo.metratura_reale || 0}m
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Input metri */}
          <TextField
            autoFocus
            label="Metri Posati"
            type="number"
            fullWidth
            value={metriPosati}
            onChange={(e) => setMetriPosati(e.target.value)}
            onKeyPress={handleKeyPress}
            error={Boolean(error)}
            helperText={error || `Metri teorici: ${cavo.metri_teorici}m`}
            disabled={loading}
            InputProps={{
              endAdornment: <Typography variant="body2" color="text.secondary">m</Typography>
            }}
            sx={{ mb: 2 }}
          />

          {/* Progress bar */}
          {metriPosati && !isNaN(parseFloat(metriPosati)) && (
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Progresso installazione
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {progressPercentage.toFixed(1)}%
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={progressPercentage}
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    backgroundColor: progressPercentage >= 100 ? 'success.main' : 'primary.main'
                  }
                }}
              />
            </Box>
          )}

          {/* Alert per completamento */}
          {progressPercentage >= 100 && (
            <Alert 
              severity="success" 
              icon={<CheckIcon />}
              sx={{ mt: 2 }}
            >
              Installazione completata! Il cavo verrà marcato come INSTALLATO.
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button 
          onClick={handleClose}
          disabled={loading}
        >
          Annulla
        </Button>
        <Button 
          onClick={handleSave}
          variant="contained"
          disabled={loading || !metriPosati.trim()}
          startIcon={loading ? null : <RulerIcon />}
        >
          {loading ? 'Salvando...' : 'Salva Metri'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InserisciMetriDialog;
