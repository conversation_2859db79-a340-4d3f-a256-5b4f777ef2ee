import React, { useState, useEffect } from 'react';
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography
} from '@mui/material';

/**
 * Componente per menu contestuale (click destro)
 * 
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Se il menu è aperto
 * @param {Object} props.anchorPosition - Posizione del menu (x, y)
 * @param {Function} props.onClose - Funzione chiamata quando il menu si chiude
 * @param {Array} props.menuItems - Array di elementi del menu
 * @param {Object} props.contextData - Dati del contesto (es. riga selezionata)
 */
const ContextMenu = ({
  open = false,
  anchorPosition = null,
  onClose = () => {},
  menuItems = [],
  contextData = null
}) => {
  const [menuOpen, setMenuOpen] = useState(open);

  useEffect(() => {
    setMenuOpen(open);
  }, [open]);

  const handleClose = () => {
    setMenuOpen(false);
    onClose();
  };

  const handleMenuItemClick = (action, item) => {
    if (item.onClick) {
      item.onClick(contextData, action);
    }
    handleClose();
  };

  // Filtra gli elementi del menu in base alle condizioni
  const getFilteredMenuItems = () => {
    return menuItems.filter(item => {
      if (item.condition && typeof item.condition === 'function') {
        return item.condition(contextData);
      }
      return true;
    });
  };

  const filteredItems = getFilteredMenuItems();

  if (!anchorPosition || filteredItems.length === 0) {
    return null;
  }

  return (
    <Menu
      open={menuOpen}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={anchorPosition}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      slotProps={{
        paper: {
          sx: {
            minWidth: 200,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            border: '1px solid rgba(0,0,0,0.1)'
          }
        }
      }}
    >
      {filteredItems.map((item, index) => {
        if (item.type === 'divider') {
          return <Divider key={`divider-${index}`} />;
        }

        if (item.type === 'header') {
          return (
            <MenuItem key={`header-${index}`} disabled sx={{ opacity: 1 }}>
              <Typography variant="subtitle2" color="primary" fontWeight="bold">
                {item.label}
              </Typography>
            </MenuItem>
          );
        }

        return (
          <MenuItem
            key={item.id || index}
            onClick={() => handleMenuItemClick(item.action, item)}
            disabled={item.disabled}
            sx={{
              py: 1,
              '&:hover': {
                backgroundColor: item.color ? `${item.color}.light` : 'action.hover'
              }
            }}
          >
            {item.icon && (
              <ListItemIcon sx={{ color: item.color || 'inherit', minWidth: 36 }}>
                {item.icon}
              </ListItemIcon>
            )}
            <ListItemText 
              primary={item.label}
              secondary={item.description}
              primaryTypographyProps={{
                fontSize: '0.875rem',
                color: item.color || 'inherit'
              }}
              secondaryTypographyProps={{
                fontSize: '0.75rem'
              }}
            />
            {item.shortcut && (
              <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
                {item.shortcut}
              </Typography>
            )}
          </MenuItem>
        );
      })}
    </Menu>
  );
};

export default ContextMenu;
