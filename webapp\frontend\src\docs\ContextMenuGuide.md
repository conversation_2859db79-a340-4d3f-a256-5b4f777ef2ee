# Guida al Menu Contestuale

## Panoramica
Il menu contestuale (click destro) è stato implementato nella pagina "Visualizza Cavi" per fornire accesso rapido alle azioni più comuni sui cavi.

## Come utilizzare il menu contestuale

### Attivazione
1. **Click destro** su qualsiasi riga della tabella cavi (attivi o spare)
2. Il menu apparirà nella posizione del cursore
3. Clicca su un'azione per eseguirla

### Azioni disponibili

#### 📋 **Informazioni**
- **Visualizza Dettagli**: Apre il dialogo con tutti i dettagli del cavo

#### ✏️ **Modifica**
- **Modifica**: Apre l'interfaccia di modifica del cavo (da implementare)
- **Elimina**: Richiede conferma ed elimina il cavo (da implementare)

#### ✅ **Selezione**
- **Seleziona/Deseleziona**: Aggiunge o rimuove il cavo dalla selezione
- Abilita automaticamente la modalità selezione se non è già attiva

#### 📋 **Copia**
- **Copia ID**: Copia l'ID del cavo negli appunti
- **Copia Dettagli**: Copia ID, tipologia, sezione e metri negli appunti

## Caratteristiche tecniche

### Componenti coinvolti
- `ContextMenu.js`: Componente riutilizzabile per il menu
- `useContextMenu.js`: Hook per gestire lo stato del menu
- `CaviFilterableTable.js`: Integrazione del menu nelle righe
- `VisualizzaCaviPage.js`: Definizione delle azioni

### Personalizzazione
Il menu è completamente personalizzabile tramite la funzione `getContextMenuItems()` che può:
- Mostrare/nascondere elementi in base al contesto
- Cambiare colori e icone
- Aggiungere nuove azioni
- Includere separatori e intestazioni

### Esempi di estensione

```javascript
// Aggiungere una nuova azione
{
  id: 'export',
  label: 'Esporta',
  icon: <ExportIcon fontSize="small" />,
  action: 'export',
  onClick: handleContextMenuAction,
  color: 'info'
}

// Azione condizionale
{
  id: 'certify',
  label: 'Certifica',
  icon: <CertificateIcon fontSize="small" />,
  action: 'certify',
  onClick: handleContextMenuAction,
  condition: (cavo) => cavo.stato_installazione === 'INSTALLATO'
}
```

## Vantaggi del menu contestuale

1. **Accesso rapido**: Azioni immediate senza navigare nei menu
2. **Contestuale**: Mostra solo le azioni pertinenti al cavo selezionato
3. **Intuitivo**: Interfaccia familiare per gli utenti desktop
4. **Efficiente**: Riduce il numero di click necessari
5. **Estensibile**: Facile aggiungere nuove funzionalità

## Prossimi sviluppi

- Integrazione con le funzioni di modifica ed eliminazione esistenti
- Azioni batch sui cavi selezionati
- Menu contestuale per le intestazioni delle colonne
- Scorciatoie da tastiera
- Azioni di esportazione e stampa
