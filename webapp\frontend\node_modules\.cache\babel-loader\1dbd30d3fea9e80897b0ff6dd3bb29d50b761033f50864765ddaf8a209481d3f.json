{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\ContextMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Menu, MenuItem, ListItemIcon, ListItemText, Divider, Typography } from '@mui/material';\n\n/**\n * Componente per menu contestuale (click destro)\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il menu è aperto\n * @param {Object} props.anchorPosition - Posizione del menu (x, y)\n * @param {Function} props.onClose - Funzione chiamata quando il menu si chiude\n * @param {Array} props.menuItems - Array di elementi del menu\n * @param {Object} props.contextData - Dati del contesto (es. riga selezionata)\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContextMenu = ({\n  open = false,\n  anchorPosition = null,\n  onClose = () => {},\n  menuItems = [],\n  contextData = null\n}) => {\n  _s();\n  const [menuOpen, setMenuOpen] = useState(open);\n  useEffect(() => {\n    setMenuOpen(open);\n  }, [open]);\n  const handleClose = () => {\n    setMenuOpen(false);\n    onClose();\n  };\n  const handleMenuItemClick = (action, item) => {\n    if (item.onClick) {\n      item.onClick(contextData, action);\n    }\n    handleClose();\n  };\n\n  // Filtra gli elementi del menu in base alle condizioni\n  const getFilteredMenuItems = () => {\n    return menuItems.filter(item => {\n      if (item.condition && typeof item.condition === 'function') {\n        return item.condition(contextData);\n      }\n      return true;\n    });\n  };\n  const filteredItems = getFilteredMenuItems();\n  if (!anchorPosition || filteredItems.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Menu, {\n    open: menuOpen,\n    onClose: handleClose,\n    anchorReference: \"anchorPosition\",\n    anchorPosition: anchorPosition,\n    transformOrigin: {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    slotProps: {\n      paper: {\n        sx: {\n          minWidth: 200,\n          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\n          border: '1px solid rgba(0,0,0,0.1)'\n        }\n      }\n    },\n    children: filteredItems.map((item, index) => {\n      if (item.type === 'divider') {\n        return /*#__PURE__*/_jsxDEV(Divider, {}, `divider-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 18\n        }, this);\n      }\n      if (item.type === 'header') {\n        return /*#__PURE__*/_jsxDEV(MenuItem, {\n          disabled: true,\n          sx: {\n            opacity: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            color: \"primary\",\n            fontWeight: \"bold\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this)\n        }, `header-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleMenuItemClick(item.action, item),\n        disabled: item.disabled,\n        sx: {\n          py: 1,\n          '&:hover': {\n            backgroundColor: item.color ? `${item.color}.light` : 'action.hover'\n          }\n        },\n        children: [item.icon && /*#__PURE__*/_jsxDEV(ListItemIcon, {\n          sx: {\n            color: item.color || 'inherit',\n            minWidth: 36\n          },\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: item.label,\n          secondary: item.description,\n          primaryTypographyProps: {\n            fontSize: '0.875rem',\n            color: item.color || 'inherit'\n          },\n          secondaryTypographyProps: {\n            fontSize: '0.75rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), item.shortcut && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          sx: {\n            ml: 2\n          },\n          children: item.shortcut\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 15\n        }, this)]\n      }, item.id || index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(ContextMenu, \"IxNd1tVco7da1WsRlc0KeQhpgaI=\");\n_c = ContextMenu;\nexport default ContextMenu;\nvar _c;\n$RefreshReg$(_c, \"ContextMenu\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "MenuItem", "ListItemIcon", "ListItemText", "Divider", "Typography", "jsxDEV", "_jsxDEV", "ContextMenu", "open", "anchorPosition", "onClose", "menuItems", "contextData", "_s", "menuOpen", "setMenuOpen", "handleClose", "handleMenuItemClick", "action", "item", "onClick", "getFilteredMenuItems", "filter", "condition", "filteredItems", "length", "anchorReference", "transform<PERSON><PERSON>in", "vertical", "horizontal", "slotProps", "paper", "sx", "min<PERSON><PERSON><PERSON>", "boxShadow", "border", "children", "map", "index", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "opacity", "variant", "color", "fontWeight", "label", "py", "backgroundColor", "icon", "primary", "secondary", "description", "primaryTypographyProps", "fontSize", "secondaryTypographyProps", "shortcut", "ml", "id", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/common/ContextMenu.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Menu,\n  MenuItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Typography\n} from '@mui/material';\n\n/**\n * Componente per menu contestuale (click destro)\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il menu è aperto\n * @param {Object} props.anchorPosition - Posizione del menu (x, y)\n * @param {Function} props.onClose - Funzione chiamata quando il menu si chiude\n * @param {Array} props.menuItems - Array di elementi del menu\n * @param {Object} props.contextData - Dati del contesto (es. riga selezionata)\n */\nconst ContextMenu = ({\n  open = false,\n  anchorPosition = null,\n  onClose = () => {},\n  menuItems = [],\n  contextData = null\n}) => {\n  const [menuOpen, setMenuOpen] = useState(open);\n\n  useEffect(() => {\n    setMenuOpen(open);\n  }, [open]);\n\n  const handleClose = () => {\n    setMenuOpen(false);\n    onClose();\n  };\n\n  const handleMenuItemClick = (action, item) => {\n    if (item.onClick) {\n      item.onClick(contextData, action);\n    }\n    handleClose();\n  };\n\n  // Filtra gli elementi del menu in base alle condizioni\n  const getFilteredMenuItems = () => {\n    return menuItems.filter(item => {\n      if (item.condition && typeof item.condition === 'function') {\n        return item.condition(contextData);\n      }\n      return true;\n    });\n  };\n\n  const filteredItems = getFilteredMenuItems();\n\n  if (!anchorPosition || filteredItems.length === 0) {\n    return null;\n  }\n\n  return (\n    <Menu\n      open={menuOpen}\n      onClose={handleClose}\n      anchorReference=\"anchorPosition\"\n      anchorPosition={anchorPosition}\n      transformOrigin={{\n        vertical: 'top',\n        horizontal: 'left',\n      }}\n      slotProps={{\n        paper: {\n          sx: {\n            minWidth: 200,\n            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\n            border: '1px solid rgba(0,0,0,0.1)'\n          }\n        }\n      }}\n    >\n      {filteredItems.map((item, index) => {\n        if (item.type === 'divider') {\n          return <Divider key={`divider-${index}`} />;\n        }\n\n        if (item.type === 'header') {\n          return (\n            <MenuItem key={`header-${index}`} disabled sx={{ opacity: 1 }}>\n              <Typography variant=\"subtitle2\" color=\"primary\" fontWeight=\"bold\">\n                {item.label}\n              </Typography>\n            </MenuItem>\n          );\n        }\n\n        return (\n          <MenuItem\n            key={item.id || index}\n            onClick={() => handleMenuItemClick(item.action, item)}\n            disabled={item.disabled}\n            sx={{\n              py: 1,\n              '&:hover': {\n                backgroundColor: item.color ? `${item.color}.light` : 'action.hover'\n              }\n            }}\n          >\n            {item.icon && (\n              <ListItemIcon sx={{ color: item.color || 'inherit', minWidth: 36 }}>\n                {item.icon}\n              </ListItemIcon>\n            )}\n            <ListItemText \n              primary={item.label}\n              secondary={item.description}\n              primaryTypographyProps={{\n                fontSize: '0.875rem',\n                color: item.color || 'inherit'\n              }}\n              secondaryTypographyProps={{\n                fontSize: '0.75rem'\n              }}\n            />\n            {item.shortcut && (\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ ml: 2 }}>\n                {item.shortcut}\n              </Typography>\n            )}\n          </MenuItem>\n        );\n      })}\n    </Menu>\n  );\n};\n\nexport default ContextMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,QACL,eAAe;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAAAC,MAAA,IAAAC,OAAA;AAUA,MAAMC,WAAW,GAAGA,CAAC;EACnBC,IAAI,GAAG,KAAK;EACZC,cAAc,GAAG,IAAI;EACrBC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,SAAS,GAAG,EAAE;EACdC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAACW,IAAI,CAAC;EAE9CV,SAAS,CAAC,MAAM;IACdiB,WAAW,CAACP,IAAI,CAAC;EACnB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEV,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxBD,WAAW,CAAC,KAAK,CAAC;IAClBL,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMO,mBAAmB,GAAGA,CAACC,MAAM,EAAEC,IAAI,KAAK;IAC5C,IAAIA,IAAI,CAACC,OAAO,EAAE;MAChBD,IAAI,CAACC,OAAO,CAACR,WAAW,EAAEM,MAAM,CAAC;IACnC;IACAF,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjC,OAAOV,SAAS,CAACW,MAAM,CAACH,IAAI,IAAI;MAC9B,IAAIA,IAAI,CAACI,SAAS,IAAI,OAAOJ,IAAI,CAACI,SAAS,KAAK,UAAU,EAAE;QAC1D,OAAOJ,IAAI,CAACI,SAAS,CAACX,WAAW,CAAC;MACpC;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMY,aAAa,GAAGH,oBAAoB,CAAC,CAAC;EAE5C,IAAI,CAACZ,cAAc,IAAIe,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;IACjD,OAAO,IAAI;EACb;EAEA,oBACEnB,OAAA,CAACP,IAAI;IACHS,IAAI,EAAEM,QAAS;IACfJ,OAAO,EAAEM,WAAY;IACrBU,eAAe,EAAC,gBAAgB;IAChCjB,cAAc,EAAEA,cAAe;IAC/BkB,eAAe,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE;IACd,CAAE;IACFC,SAAS,EAAE;MACTC,KAAK,EAAE;QACLC,EAAE,EAAE;UACFC,QAAQ,EAAE,GAAG;UACbC,SAAS,EAAE,6BAA6B;UACxCC,MAAM,EAAE;QACV;MACF;IACF,CAAE;IAAAC,QAAA,EAEDZ,aAAa,CAACa,GAAG,CAAC,CAAClB,IAAI,EAAEmB,KAAK,KAAK;MAClC,IAAInB,IAAI,CAACoB,IAAI,KAAK,SAAS,EAAE;QAC3B,oBAAOjC,OAAA,CAACH,OAAO,MAAM,WAAWmC,KAAK,EAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAC7C;MAEA,IAAIxB,IAAI,CAACoB,IAAI,KAAK,QAAQ,EAAE;QAC1B,oBACEjC,OAAA,CAACN,QAAQ;UAAyB4C,QAAQ;UAACZ,EAAE,EAAE;YAAEa,OAAO,EAAE;UAAE,CAAE;UAAAT,QAAA,eAC5D9B,OAAA,CAACF,UAAU;YAAC0C,OAAO,EAAC,WAAW;YAACC,KAAK,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAAAZ,QAAA,EAC9DjB,IAAI,CAAC8B;UAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GAHA,UAAUL,KAAK,EAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAItB,CAAC;MAEf;MAEA,oBACErC,OAAA,CAACN,QAAQ;QAEPoB,OAAO,EAAEA,CAAA,KAAMH,mBAAmB,CAACE,IAAI,CAACD,MAAM,EAAEC,IAAI,CAAE;QACtDyB,QAAQ,EAAEzB,IAAI,CAACyB,QAAS;QACxBZ,EAAE,EAAE;UACFkB,EAAE,EAAE,CAAC;UACL,SAAS,EAAE;YACTC,eAAe,EAAEhC,IAAI,CAAC4B,KAAK,GAAG,GAAG5B,IAAI,CAAC4B,KAAK,QAAQ,GAAG;UACxD;QACF,CAAE;QAAAX,QAAA,GAEDjB,IAAI,CAACiC,IAAI,iBACR9C,OAAA,CAACL,YAAY;UAAC+B,EAAE,EAAE;YAAEe,KAAK,EAAE5B,IAAI,CAAC4B,KAAK,IAAI,SAAS;YAAEd,QAAQ,EAAE;UAAG,CAAE;UAAAG,QAAA,EAChEjB,IAAI,CAACiC;QAAI;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACf,eACDrC,OAAA,CAACJ,YAAY;UACXmD,OAAO,EAAElC,IAAI,CAAC8B,KAAM;UACpBK,SAAS,EAAEnC,IAAI,CAACoC,WAAY;UAC5BC,sBAAsB,EAAE;YACtBC,QAAQ,EAAE,UAAU;YACpBV,KAAK,EAAE5B,IAAI,CAAC4B,KAAK,IAAI;UACvB,CAAE;UACFW,wBAAwB,EAAE;YACxBD,QAAQ,EAAE;UACZ;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDxB,IAAI,CAACwC,QAAQ,iBACZrD,OAAA,CAACF,UAAU;UAAC0C,OAAO,EAAC,SAAS;UAACC,KAAK,EAAC,gBAAgB;UAACf,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAAxB,QAAA,EAChEjB,IAAI,CAACwC;QAAQ;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACb;MAAA,GA9BIxB,IAAI,CAAC0C,EAAE,IAAIvB,KAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+Bb,CAAC;IAEf,CAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEX,CAAC;AAAC9B,EAAA,CAlHIN,WAAW;AAAAuD,EAAA,GAAXvD,WAAW;AAoHjB,eAAeA,WAAW;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}