import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Alert,
  Chip,
  Grid,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Cable as CableIcon,
  FiberManualRecord as BobinaIcon,
  SwapHoriz as SwapIcon
} from '@mui/icons-material';

/**
 * Dialogo per modificare la bobina associata a un cavo
 * 
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Se il dialogo è aperto
 * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude
 * @param {Object} props.cavo - Dati del cavo selezionato
 * @param {Array} props.bobineDisponibili - Lista delle bobine disponibili
 * @param {Function} props.onSave - Funzione chiamata quando si salva la modifica
 * @param {boolean} props.loading - Indica se il salvataggio è in corso
 */
const ModificaBobinaDialog = ({
  open = false,
  onClose = () => {},
  cavo = null,
  bobineDisponibili = [],
  onSave = () => {},
  loading = false
}) => {
  const [selectedBobina, setSelectedBobina] = useState(null);
  const [motivazione, setMotivazione] = useState('');
  const [error, setError] = useState('');

  // Reset quando si apre il dialogo
  useEffect(() => {
    if (open && cavo) {
      // Trova la bobina attualmente associata
      const bobinaAttuale = bobineDisponibili.find(b => b.id_bobina === cavo.id_bobina);
      setSelectedBobina(bobinaAttuale || null);
      setMotivazione('');
      setError('');
    }
  }, [open, cavo, bobineDisponibili]);

  const handleSave = () => {
    if (!selectedBobina) {
      setError('Selezionare una bobina');
      return;
    }

    if (!motivazione.trim()) {
      setError('Inserire una motivazione per la modifica');
      return;
    }

    // Verifica compatibilità
    if (selectedBobina.tipologia !== cavo.tipologia || selectedBobina.sezione !== cavo.sezione) {
      setError('La bobina selezionata non è compatibile con il cavo (tipologia/sezione diverse)');
      return;
    }

    setError('');
    onSave(cavo.id_cavo, selectedBobina.id_bobina, motivazione);
  };

  const handleClose = () => {
    if (!loading) {
      setError('');
      onClose();
    }
  };

  if (!cavo) return null;

  // Filtra bobine compatibili
  const bobineCompatibili = bobineDisponibili.filter(bobina => 
    bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione
  );

  const bobinaAttuale = bobineDisponibili.find(b => b.id_bobina === cavo.id_bobina);

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SettingsIcon color="primary" />
          <Typography variant="h6">
            Modifica Bobina
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          {/* Informazioni cavo */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12}>
              <Box sx={{ 
                p: 2, 
                backgroundColor: 'grey.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <CableIcon fontSize="small" color="primary" />
                  <Typography variant="subtitle1" fontWeight="bold">
                    {cavo.id_cavo}
                  </Typography>
                  <Chip label="INSTALLATO" size="small" color="success" />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  <strong>Tipologia:</strong> {cavo.tipologia} | <strong>Sezione:</strong> {cavo.sezione}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Metri posati:</strong> {cavo.metratura_reale || 0}m | <strong>Bobina attuale:</strong> {cavo.id_bobina || 'N/D'}
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Bobina attuale */}
          {bobinaAttuale && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Bobina Attuale
              </Typography>
              <Box sx={{ 
                p: 2, 
                backgroundColor: 'primary.50', 
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'primary.200'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <BobinaIcon fontSize="small" color="primary" />
                  <Typography variant="body1" fontWeight="bold">
                    {bobinaAttuale.id_bobina}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    - {bobinaAttuale.metri_residui || 0}m residui
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}

          {/* Selezione nuova bobina */}
          <Autocomplete
            value={selectedBobina}
            onChange={(event, newValue) => setSelectedBobina(newValue)}
            options={bobineCompatibili}
            getOptionLabel={(option) => `${option.id_bobina} (${option.metri_residui || 0}m residui)`}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                  <BobinaIcon fontSize="small" />
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2" fontWeight="bold">
                      {option.id_bobina}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {option.tipologia} {option.sezione} - {option.metri_residui || 0}m residui
                    </Typography>
                  </Box>
                  {option.id_bobina === cavo.id_bobina && (
                    <Chip label="Attuale" size="small" color="primary" />
                  )}
                </Box>
              </Box>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Seleziona Bobina"
                error={Boolean(error && !selectedBobina)}
                helperText={`${bobineCompatibili.length} bobine compatibili disponibili`}
              />
            )}
            sx={{ mb: 2 }}
            disabled={loading}
          />

          {/* Motivazione */}
          <TextField
            label="Motivazione della modifica"
            multiline
            rows={3}
            fullWidth
            value={motivazione}
            onChange={(e) => setMotivazione(e.target.value)}
            error={Boolean(error && !motivazione.trim())}
            helperText={error || "Specificare il motivo della modifica (es. bobina danneggiata, ottimizzazione, ecc.)"}
            disabled={loading}
            sx={{ mb: 2 }}
          />

          {/* Alert per cambio bobina */}
          {selectedBobina && selectedBobina.id_bobina !== cavo.id_bobina && (
            <Alert 
              severity="info" 
              icon={<SwapIcon />}
              sx={{ mt: 2 }}
            >
              <Typography variant="body2">
                <strong>Cambio bobina:</strong> da {cavo.id_bobina || 'N/D'} a {selectedBobina.id_bobina}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Questa operazione aggiornerà i metri residui delle bobine coinvolte.
              </Typography>
            </Alert>
          )}

          {/* Alert per bobine incompatibili */}
          {bobineCompatibili.length === 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Nessuna bobina compatibile disponibile per questo tipo di cavo.
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button 
          onClick={handleClose}
          disabled={loading}
        >
          Annulla
        </Button>
        <Button 
          onClick={handleSave}
          variant="contained"
          disabled={loading || !selectedBobina || !motivazione.trim() || selectedBobina.id_bobina === cavo.id_bobina}
          startIcon={loading ? null : <SwapIcon />}
        >
          {loading ? 'Salvando...' : 'Modifica Bobina'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ModificaBobinaDialog;
