{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar, FormControl, InputLabel, Select, MenuItem, Stack } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { Cable as CableIcon, CheckCircle as CheckCircleIcon, Schedule as ScheduleIcon, Link as LinkIcon, LinkOff as LinkOffIcon, Timeline as TimelineIcon, CheckBox as CheckBoxIcon, CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon, Visibility as VisibilityIcon, Edit as EditIcon, Delete as DeleteIcon, SelectAll as SelectAllIcon, ContentCopy as CopyIcon, Settings as SettingsIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialog from '../../components/cavi/InserisciMetriDialog';\nimport ModificaBobinaDialog from '../../components/cavi/ModificaBobinaDialog';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: (caviAttiviData === null || caviAttiviData === void 0 ? void 0 : caviAttiviData.length) || 0,\n      caviSpare: (caviSpareData === null || caviSpareData === void 0 ? void 0 : caviSpareData.length) || 0,\n      totale: tuttiCavi.length\n    });\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').length;\n    const caviDaInstallare = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'DA_INSTALLARE').length;\n    const caviInCorso = tuttiCavi.filter(cavo => cavo.stato_installazione === 'In corso' || cavo.stato_installazione === 'IN_CORSO').length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo => cavo.collegamenti === 3 && cavo.responsabile_partenza && cavo.responsabile_arrivo).length;\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola percentuali\n    const percentualeInstallazione = totaleCavi > 0 ? Math.round(caviInstallati / totaleCavi * 100) : 0;\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round(caviCollegati / totaleCavi * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async cantiereIdToUse => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = event => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n  const handleCaviAttiviSelectionChange = selectedIds => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n  const handleCaviSpareSelectionChange = selectedIds => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Implementa logica di modifica\n        showNotification(`Modifica cavo ${cavo.id_cavo} - Funzione da implementare`, 'info');\n        break;\n      case 'delete':\n        // Implementa logica di eliminazione\n        if (window.confirm(`Sei sicuro di voler eliminare il cavo ${cavo.id_cavo}?`)) {\n          showNotification(`Eliminazione cavo ${cavo.id_cavo} - Funzione da implementare`, 'warning');\n        }\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo);\n        showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        break;\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n        navigator.clipboard.writeText(details);\n        showNotification('Dettagli cavo copiati negli appunti', 'success');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = cavo => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === (cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo)) ? selectedCaviAttivi.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) : selectedCaviSpare.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo);\n    return [{\n      type: 'header',\n      label: `Cavo ${(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) || ''}`\n    }, {\n      id: 'view_details',\n      label: 'Visualizza Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 15\n      }, this),\n      action: 'view_details',\n      onClick: handleContextMenuAction\n    }, {\n      type: 'divider'\n    }, {\n      id: 'edit',\n      label: 'Modifica',\n      icon: /*#__PURE__*/_jsxDEV(EditIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 15\n      }, this),\n      action: 'edit',\n      onClick: handleContextMenuAction,\n      color: 'primary'\n    }, {\n      id: 'delete',\n      label: 'Elimina',\n      icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 15\n      }, this),\n      action: 'delete',\n      onClick: handleContextMenuAction,\n      color: 'error'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'select',\n      label: isSelected ? 'Deseleziona' : 'Seleziona',\n      icon: /*#__PURE__*/_jsxDEV(SelectAllIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 743,\n        columnNumber: 15\n      }, this),\n      action: 'select',\n      onClick: handleContextMenuAction,\n      color: isSelected ? 'warning' : 'success'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'copy_id',\n      label: 'Copia ID',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 15\n      }, this),\n      action: 'copy_id',\n      onClick: handleContextMenuAction,\n      shortcut: 'Ctrl+C'\n    }, {\n      id: 'copy_details',\n      label: 'Copia Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 762,\n        columnNumber: 15\n      }, this),\n      action: 'copy_details',\n      onClick: handleContextMenuAction,\n      description: 'Copia ID, tipologia, sezione e metri'\n    }];\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviInstallati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 789,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeInstallazione >= 80 ? 'success.main' : statistics.percentualeInstallazione >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.percentualeInstallazione, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"Installazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.metriInstallati, \"m installati\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 775,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 774,\n    columnNumber: 5\n  }, this);\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 882,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 894,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 903,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 902,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 850,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 919,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 916,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 940,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 929,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [revisioniDisponibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Visualizzazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 250\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Revisione da Visualizzare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: revisioneSelezionata || 'corrente',\n              onChange: handleRevisioneChange,\n              label: \"Revisione da Visualizzare\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"corrente\",\n                children: [\"\\uD83D\\uDCCB Revisione Corrente \", revisioneCorrente && `(${revisioneCorrente})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 21\n              }, this), revisioniDisponibili.map(rev => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: rev.revisione,\n                children: [\"\\uD83D\\uDCDA \", rev.revisione, \" (\", rev.cavi_count, \" cavi)\", rev.revisione === revisioneCorrente && ' - Attuale']\n              }, rev.revisione, true, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: revisioneSelezionata ? `Storico: ${revisioneSelezionata}` : `Corrente: ${revisioneCorrente || 'N/A'}`,\n            color: revisioneSelezionata ? \"secondary\" : \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 13\n      }, this), renderDashboard(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Attivi \", caviAttivi.length > 0 ? `(${caviAttivi.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 2,\n              alignItems: 'center'\n            },\n            children: [selectionEnabled && getTotalSelectedCount() > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${getTotalSelectedCount()} cavi selezionati`,\n              color: \"primary\",\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: selectionEnabled ? \"contained\" : \"outlined\",\n              color: \"primary\",\n              onClick: handleSelectionToggle,\n              startIcon: selectionEnabled ? /*#__PURE__*/_jsxDEV(CheckBoxIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 49\n              }, this) : /*#__PURE__*/_jsxDEV(CheckBoxOutlineBlankIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 68\n              }, this),\n              children: selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1009,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 13\n        }, this), process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviAttivi,\n          onSelectionChange: handleCaviAttiviSelectionChange,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1029,\n          columnNumber: 13\n        }, this), caviAttivi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1041,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviSpare,\n          onSelectionChange: handleCaviSpareSelectionChange,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1054,\n          columnNumber: 13\n        }, this), caviSpare.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1065,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1048,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati invece di ricaricare la pagina\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'eliminazione del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi();\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1083,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1077,\n        columnNumber: 11\n      }, this), openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openModificaCavoDialog,\n        onClose: () => setOpenModificaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"sm\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati immediatamente\n              console.log('Ricaricamento dati dopo operazione...');\n              // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                try {\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante la modifica del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            console.log('Ricaricamento dati dopo errore...');\n            fetchCavi(true);\n          },\n          initialOption: \"modificaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1136,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1130,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openAggiungiCavoDialog,\n        onClose: () => setOpenAggiungiCavoDialog(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenAggiungiCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina immediatamente\n                  console.log('Tentativo di ricaricamento della pagina...');\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'aggiunta del cavo:', message);\n            // Mostra un messaggio di errore con Snackbar\n            showNotification(`Errore: ${message}`, 'error');\n            // Chiudi il dialogo\n            setOpenAggiungiCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi(true);\n          },\n          initialOption: \"aggiungiCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1182,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 4000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1233,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1227,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 951,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 914,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"yfxlofz9Z6arsBkUVY4NlZdHnh4=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "InfoIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Schedule", "ScheduleIcon", "Link", "LinkIcon", "<PERSON><PERSON><PERSON>", "LinkOffIcon", "Timeline", "TimelineIcon", "CheckBox", "CheckBoxIcon", "CheckBoxOutlineBlank", "CheckBoxOutlineBlankIcon", "Visibility", "VisibilityIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "SelectAll", "SelectAllIcon", "ContentCopy", "CopyIcon", "Settings", "SettingsIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "InserisciMetriDialog", "ModificaBobinaDialog", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "selectionEnabled", "setSelectionEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSele<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCaviSpare", "setSelectedCaviSpare", "statistics", "setStatistics", "totaleCavi", "caviInstallati", "caviDaInstallare", "caviInCorso", "caviCollegati", "caviNonCollegati", "percentualeInstallazione", "percentualeCollegamento", "metriTotali", "metriInstallati", "metriR<PERSON><PERSON><PERSON>", "revisioniDisponibili", "setRevisioniDisponibili", "revisioneSelezionata", "setRevisioneSelezionata", "revisioneCorrente", "setRevisioneCorrente", "calculateStatistics", "caviAttiviData", "caviSpareData", "tuttiCavi", "length", "console", "log", "totale", "filter", "cavo", "stato_installazione", "colle<PERSON>nti", "responsabile_partenza", "responsabile_arrivo", "Math", "round", "reduce", "sum", "parseFloat", "metri_te<PERSON>ci", "metratura_reale", "newStatistics", "loadStatiInstallazione", "setStatiInstallazione", "loadRevisioni", "cantiereIdToUse", "revisioneCorrenteData", "getRevisioneCorrente", "revisione_corrente", "revisioniData", "getRevisioniDisponibili", "revisioni", "handleRevisioneChange", "event", "nuovaRevisione", "target", "value", "filters", "setFilters", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "attiviError", "caviSpareTra<PERSON>ttivi", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "handleSelectionToggle", "handleCaviAttiviSelectionChange", "selectedIds", "handleCaviSpareSelectionChange", "getAllSelectedCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id_cavo", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTotalSelectedCount", "handleContextMenuAction", "action", "confirm", "some", "isSelected", "id", "navigator", "clipboard", "writeText", "details", "sezione", "getContextMenuItems", "type", "label", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "shortcut", "description", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "container", "item", "xs", "md", "gutterBottom", "sistema", "utility", "colore_cavo", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "comanda_arrivo", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "className", "flexDirection", "mt", "size", "gap", "min<PERSON><PERSON><PERSON>", "onChange", "rev", "revisione", "cavi_count", "startIcon", "process", "env", "NODE_ENV", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisione_ufficiale", "<PERSON><PERSON><PERSON>", "onSelectionChange", "contextMenuItems", "onContextMenuAction", "onSuccess", "onError", "alert", "initialOption", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport {\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Schedule as ScheduleIcon,\n  Link as LinkIcon,\n  LinkOff as LinkOffIcon,\n  Timeline as TimelineIcon,\n  CheckBox as CheckBoxIcon,\n  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,\n  Visibility as VisibilityIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  SelectAll as SelectAllIcon,\n  ContentCopy as CopyIcon,\n  Settings as SettingsIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialog from '../../components/cavi/InserisciMetriDialog';\nimport ModificaBobinaDialog from '../../components/cavi/ModificaBobinaDialog';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: caviAttiviData?.length || 0,\n      caviSpare: caviSpareData?.length || 0,\n      totale: tuttiCavi.length\n    });\n\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Installato' ||\n      cavo.stato_installazione === 'INSTALLATO' ||\n      cavo.stato_installazione === 'POSATO'\n    ).length;\n\n    const caviDaInstallare = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Da installare' ||\n      cavo.stato_installazione === 'DA_INSTALLARE'\n    ).length;\n\n    const caviInCorso = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'In corso' ||\n      cavo.stato_installazione === 'IN_CORSO'\n    ).length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo =>\n      cavo.collegamenti === 3 &&\n      cavo.responsabile_partenza &&\n      cavo.responsabile_arrivo\n    ).length;\n\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola percentuali\n    const percentualeInstallazione = totaleCavi > 0 ? Math.round((caviInstallati / totaleCavi) * 100) : 0;\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi\n      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')\n      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async (cantiereIdToUse) => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = (event) => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n\n  const handleCaviAttiviSelectionChange = (selectedIds) => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n\n  const handleCaviSpareSelectionChange = (selectedIds) => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Implementa logica di modifica\n        showNotification(`Modifica cavo ${cavo.id_cavo} - Funzione da implementare`, 'info');\n        break;\n      case 'delete':\n        // Implementa logica di eliminazione\n        if (window.confirm(`Sei sicuro di voler eliminare il cavo ${cavo.id_cavo}?`)) {\n          showNotification(`Eliminazione cavo ${cavo.id_cavo} - Funzione da implementare`, 'warning');\n        }\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo);\n        showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        break;\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n        navigator.clipboard.writeText(details);\n        showNotification('Dettagli cavo copiati negli appunti', 'success');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = (cavo) => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === cavo?.id_cavo)\n      ? selectedCaviAttivi.includes(cavo?.id_cavo)\n      : selectedCaviSpare.includes(cavo?.id_cavo);\n\n    return [\n      {\n        type: 'header',\n        label: `Cavo ${cavo?.id_cavo || ''}`\n      },\n      {\n        id: 'view_details',\n        label: 'Visualizza Dettagli',\n        icon: <VisibilityIcon fontSize=\"small\" />,\n        action: 'view_details',\n        onClick: handleContextMenuAction\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'edit',\n        label: 'Modifica',\n        icon: <EditIcon fontSize=\"small\" />,\n        action: 'edit',\n        onClick: handleContextMenuAction,\n        color: 'primary'\n      },\n      {\n        id: 'delete',\n        label: 'Elimina',\n        icon: <DeleteIcon fontSize=\"small\" />,\n        action: 'delete',\n        onClick: handleContextMenuAction,\n        color: 'error'\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'select',\n        label: isSelected ? 'Deseleziona' : 'Seleziona',\n        icon: <SelectAllIcon fontSize=\"small\" />,\n        action: 'select',\n        onClick: handleContextMenuAction,\n        color: isSelected ? 'warning' : 'success'\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'copy_id',\n        label: 'Copia ID',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_id',\n        onClick: handleContextMenuAction,\n        shortcut: 'Ctrl+C'\n      },\n      {\n        id: 'copy_details',\n        label: 'Copia Dettagli',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_details',\n        onClick: handleContextMenuAction,\n        description: 'Copia ID, tipologia, sezione e metri'\n      }\n    ];\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckCircleIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviInstallati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Installati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <LinkIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCollegati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Collegati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeInstallazione >= 80 ? 'success.main' :\n                     statistics.percentualeInstallazione >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.percentualeInstallazione}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              Installazione\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.metriInstallati}m installati\n            </Typography>\n          </Box>\n        </Stack>\n      </Stack>\n    </Paper>\n  );\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                {/* sh field is now a spare field (kept in DB but hidden in UI) */}\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Selettore Revisione */}\n          {revisioniDisponibili.length > 0 && (\n            <Paper sx={{ p: 2, mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Typography variant=\"h6\">Visualizzazione:</Typography>\n                <FormControl size=\"small\" sx={{ minWidth: 250 }}>\n                  <InputLabel>Revisione da Visualizzare</InputLabel>\n                  <Select\n                    value={revisioneSelezionata || 'corrente'}\n                    onChange={handleRevisioneChange}\n                    label=\"Revisione da Visualizzare\"\n                  >\n                    <MenuItem value=\"corrente\">\n                      📋 Revisione Corrente {revisioneCorrente && `(${revisioneCorrente})`}\n                    </MenuItem>\n                    {revisioniDisponibili.map((rev) => (\n                      <MenuItem key={rev.revisione} value={rev.revisione}>\n                        📚 {rev.revisione} ({rev.cavi_count} cavi)\n                        {rev.revisione === revisioneCorrente && ' - Attuale'}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n                <Chip\n                  label={\n                    revisioneSelezionata\n                      ? `Storico: ${revisioneSelezionata}`\n                      : `Corrente: ${revisioneCorrente || 'N/A'}`\n                  }\n                  color={revisioneSelezionata ? \"secondary\" : \"primary\"}\n                  variant=\"outlined\"\n                />\n              </Box>\n            </Paper>\n          )}\n\n          {/* Dashboard con statistiche avanzate */}\n          {renderDashboard()}\n\n          {/* Sezione Cavi Attivi */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Attivi {caviAttivi.length > 0 ? `(${caviAttivi.length})` : ''}\n              </Typography>\n\n              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>\n                {/* Contatore selezione */}\n                {selectionEnabled && getTotalSelectedCount() > 0 && (\n                  <Chip\n                    label={`${getTotalSelectedCount()} cavi selezionati`}\n                    color=\"primary\"\n                    variant=\"outlined\"\n                  />\n                )}\n\n                {/* Pulsante modalità selezione */}\n                <Button\n                  variant={selectionEnabled ? \"contained\" : \"outlined\"}\n                  color=\"primary\"\n                  onClick={handleSelectionToggle}\n                  startIcon={selectionEnabled ? <CheckBoxIcon /> : <CheckBoxOutlineBlankIcon />}\n                >\n                  {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}\n                </Button>\n              </Box>\n            </Box>\n\n            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (\n              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                {Object.keys(caviAttivi[0]).map(key => (\n                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                ))}\n              </Box>\n            )}\n\n            <CaviFilterableTable\n              cavi={caviAttivi}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n              revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviAttivi}\n              onSelectionChange={handleCaviAttiviSelectionChange}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n            />\n            {caviAttivi.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n            </Box>\n            <CaviFilterableTable\n              cavi={caviSpare}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviSpare}\n              onSelectionChange={handleCaviSpareSelectionChange}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n            />\n            {caviSpare.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"sm\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati immediatamente\n                  console.log('Ricaricamento dati dopo operazione...');\n                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    try {\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                console.log('Ricaricamento dati dopo errore...');\n                fetchCavi(true);\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog open={openAggiungiCavoDialog} onClose={() => setOpenAggiungiCavoDialog(false)} maxWidth=\"sm\" fullWidth>\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina immediatamente\n                      console.log('Tentativo di ricaricamento della pagina...');\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un messaggio di errore con Snackbar\n                showNotification(`Errore: ${message}`, 'error');\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi(true);\n              }}\n              initialOption=\"aggiungiCavo\"\n            />\n          </Dialog>\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SACEC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,oBAAoB,IAAIC,wBAAwB,EAChDC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,WAAW,IAAIC,QAAQ,EACvBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEkB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGtB,gBAAgB,CAAC,CAAC;EACpL,MAAMuB,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiF,YAAY,EAAEC,eAAe,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmF,UAAU,EAAEC,aAAa,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqF,SAAS,EAAEC,YAAY,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuF,OAAO,EAAEC,UAAU,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyF,KAAK,EAAEC,QAAQ,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAAC2F,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC;IAAE6F,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACnG;;EAEA;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACwG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAAC0G,UAAU,EAAEC,aAAa,CAAC,GAAG3G,QAAQ,CAAC;IAC3C4G,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,wBAAwB,EAAE,CAAC;IAC3BC,uBAAuB,EAAE,CAAC;IAC1BC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAIF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACyH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC2H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;;EAE9D;;EAEA;EACA,MAAM6H,mBAAmB,GAAGA,CAACC,cAAc,EAAEC,aAAa,KAAK;IAC7D,MAAMC,SAAS,GAAG,CAAC,IAAIF,cAAc,IAAI,EAAE,CAAC,EAAE,IAAIC,aAAa,IAAI,EAAE,CAAC,CAAC;IAEvE,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3ChD,UAAU,EAAE,CAAA2C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,MAAM,KAAI,CAAC;MACvC5C,SAAS,EAAE,CAAA0C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,MAAM,KAAI,CAAC;MACrCG,MAAM,EAAEJ,SAAS,CAACC;IACpB,CAAC,CAAC;IAEF,MAAMrB,UAAU,GAAGoB,SAAS,CAACC,MAAM;;IAEnC;IACA,MAAMpB,cAAc,GAAGmB,SAAS,CAACK,MAAM,CAACC,IAAI,IAC1CA,IAAI,CAACC,mBAAmB,KAAK,YAAY,IACzCD,IAAI,CAACC,mBAAmB,KAAK,YAAY,IACzCD,IAAI,CAACC,mBAAmB,KAAK,QAC/B,CAAC,CAACN,MAAM;IAER,MAAMnB,gBAAgB,GAAGkB,SAAS,CAACK,MAAM,CAACC,IAAI,IAC5CA,IAAI,CAACC,mBAAmB,KAAK,eAAe,IAC5CD,IAAI,CAACC,mBAAmB,KAAK,eAC/B,CAAC,CAACN,MAAM;IAER,MAAMlB,WAAW,GAAGiB,SAAS,CAACK,MAAM,CAACC,IAAI,IACvCA,IAAI,CAACC,mBAAmB,KAAK,UAAU,IACvCD,IAAI,CAACC,mBAAmB,KAAK,UAC/B,CAAC,CAACN,MAAM;;IAER;IACA,MAAMjB,aAAa,GAAGgB,SAAS,CAACK,MAAM,CAACC,IAAI,IACzCA,IAAI,CAACE,YAAY,KAAK,CAAC,IACvBF,IAAI,CAACG,qBAAqB,IAC1BH,IAAI,CAACI,mBACP,CAAC,CAACT,MAAM;IAER,MAAMhB,gBAAgB,GAAGL,UAAU,GAAGI,aAAa;;IAEnD;IACA,MAAME,wBAAwB,GAAGN,UAAU,GAAG,CAAC,GAAG+B,IAAI,CAACC,KAAK,CAAE/B,cAAc,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IACrG,MAAMO,uBAAuB,GAAGP,UAAU,GAAG,CAAC,GAAG+B,IAAI,CAACC,KAAK,CAAE5B,aAAa,GAAGJ,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEnG;IACA,MAAMQ,WAAW,GAAGY,SAAS,CAACa,MAAM,CAAC,CAACC,GAAG,EAAER,IAAI,KAAKQ,GAAG,IAAIC,UAAU,CAACT,IAAI,CAACU,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnG,MAAM3B,eAAe,GAAGW,SAAS,CAC9BK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,mBAAmB,KAAK,YAAY,IAAID,IAAI,CAACC,mBAAmB,KAAK,YAAY,IAAID,IAAI,CAACC,mBAAmB,KAAK,QAAQ,CAAC,CAC/IM,MAAM,CAAC,CAACC,GAAG,EAAER,IAAI,KAAKQ,GAAG,IAAIC,UAAU,CAACT,IAAI,CAACW,eAAe,CAAC,IAAIF,UAAU,CAACT,IAAI,CAACU,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5G,MAAM1B,cAAc,GAAGF,WAAW,GAAGC,eAAe;IAEpD,MAAM6B,aAAa,GAAG;MACpBtC,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,wBAAwB;MACxBC,uBAAuB;MACvBC,WAAW,EAAEuB,IAAI,CAACC,KAAK,CAACxB,WAAW,CAAC;MACpCC,eAAe,EAAEsB,IAAI,CAACC,KAAK,CAACvB,eAAe,CAAC;MAC5CC,cAAc,EAAEqB,IAAI,CAACC,KAAK,CAACtB,cAAc;IAC3C,CAAC;IAEDY,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEe,aAAa,CAAC;IAC1DvC,aAAa,CAACuC,aAAa,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAOC,eAAe,IAAK;IAC/C,IAAI;MACFpB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEmB,eAAe,CAAC;;MAEnE;MACA,MAAMC,qBAAqB,GAAG,MAAM9F,WAAW,CAAC+F,oBAAoB,CAACF,eAAe,CAAC;MACrFpB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoB,qBAAqB,CAAC;MACzD3B,oBAAoB,CAAC2B,qBAAqB,CAACE,kBAAkB,CAAC;;MAE9D;MACA,MAAMC,aAAa,GAAG,MAAMjG,WAAW,CAACkG,uBAAuB,CAACL,eAAe,CAAC;MAChFpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuB,aAAa,CAAC;MACpDlC,uBAAuB,CAACkC,aAAa,CAACE,SAAS,IAAI,EAAE,CAAC;;MAEtD;MACA;MACA1B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACvE,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdyC,OAAO,CAACzC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAMoE,qBAAqB,GAAIC,KAAK,IAAK;IACvC,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;;IAEzC;IACA;IACA;IACA,IAAIF,cAAc,KAAK,EAAE,IAAIA,cAAc,KAAK,UAAU,EAAE;MAC1DrC,uBAAuB,CAAC,EAAE,CAAC;MAC3BQ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,MAAM;MACLT,uBAAuB,CAACqC,cAAc,CAAC;MACvC7B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4B,cAAc,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGnK,QAAQ,CAAC;IACrCuI,mBAAmB,EAAE,EAAE;IACvB6B,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEnB,qBAAqB,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACwK,aAAa,EAAEC,gBAAgB,CAAC,GAAGzK,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA;EACA,MAAM0K,SAAS,GAAG,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI;MACF,IAAI,CAACA,aAAa,EAAE;QAClBnF,UAAU,CAAC,IAAI,CAAC;MAClB;MACA0C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEpD,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACfmD,OAAO,CAACzC,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAI8D,eAAe,GAAGvE,UAAU;MAChC,IAAI,CAACuE,eAAe,EAAE;QACpBA,eAAe,GAAGsB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5D3C,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEmB,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpBpB,OAAO,CAACzC,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA0C,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAI2C,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAMrH,WAAW,CAACsH,OAAO,CAACzB,eAAe,EAAE,CAAC,EAAEY,OAAO,CAAC;QAC/DhC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2C,MAAM,GAAGA,MAAM,CAAC7C,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAO+C,WAAW,EAAE;QACpB9C,OAAO,CAACzC,KAAK,CAAC,yCAAyC,EAAEuF,WAAW,CAAC;QACrE;QACAF,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAAC7C,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAMgD,kBAAkB,GAAGH,MAAM,CAACzC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC4C,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAID,kBAAkB,CAAChD,MAAM,GAAG,CAAC,EAAE;UACjCC,OAAO,CAACzC,KAAK,CAAC,wEAAwE,EAAEwF,kBAAkB,CAAC;QAC7G;MACF;MAEA7F,aAAa,CAAC0F,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIK,KAAK,GAAG,EAAE;MACd,IAAI;QACFjD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DgD,KAAK,GAAG,MAAM1H,WAAW,CAAC2H,YAAY,CAAC9B,eAAe,CAAC;QACvDpB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEgD,KAAK,GAAGA,KAAK,CAAClD,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIkD,KAAK,IAAIA,KAAK,CAAClD,MAAM,GAAG,CAAC,EAAE;UAC7BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgD,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBnD,OAAO,CAACzC,KAAK,CAAC,8DAA8D,EAAE4F,UAAU,CAAC;QACzF;QACA,IAAI;UACFnD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CgD,KAAK,GAAG,MAAM1H,WAAW,CAACsH,OAAO,CAACzB,eAAe,EAAE,CAAC,CAAC;UACrDpB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEgD,KAAK,GAAGA,KAAK,CAAClD,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOqD,aAAa,EAAE;UACtBpD,OAAO,CAACzC,KAAK,CAAC,mCAAmC,EAAE6F,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACA7F,YAAY,CAAC6F,KAAK,IAAI,EAAE,CAAC;;MAIzB;MACAzF,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdyC,OAAO,CAACzC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAyF,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzEzD,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7EyD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR,IAAI,CAACnB,aAAa,EAAE;QAClBnF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACAvF,SAAS,CAAC,MAAM;IACd;IACAkJ,sBAAsB,CAAC,CAAC;IAExB,MAAM4C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF7D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM6D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C3C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC6D,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVtG,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIyG,kBAAkB,GAAGrB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIqB,oBAAoB,GAAGtB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvE3C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAE8D,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGhE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE5D,IAAI,CAAC;;QAEjC;QACA2D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAAC3C,MAAM,EAAEkE,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGxB,YAAY,CAACwB,GAAG,CAACD,CAAC,CAAC;UAC/BjE,OAAO,CAACC,GAAG,CAAC,GAAGiE,GAAG,KAAKxB,YAAY,CAACC,OAAO,CAACuB,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAA7H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8H,IAAI,MAAK,eAAe,EAAE;UAClCnE,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAI5D,IAAI,CAAC+H,WAAW,EAAE;YACpBpE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE5D,IAAI,CAAC+H,WAAW,CAAC;YACrEL,kBAAkB,GAAG1H,IAAI,CAAC+H,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAG3H,IAAI,CAACiI,aAAa,IAAI,YAAYjI,IAAI,CAAC+H,WAAW,EAAE;;YAE3E;YACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEhE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8D,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACF/D,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAM6D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAImB,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC5E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmF,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBpE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmF,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEhE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8D,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVvF,OAAO,CAACzC,KAAK,CAAC,6CAA6C,EAAEgI,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9F/D,OAAO,CAACwF,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAtB,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEhE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE8D,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBvG,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMmI,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtD/D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEwF,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxBjI,QAAQ,CAAC,2BAA2BuG,kBAAkB,mCAAmC,CAAC;UAC1FzG,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAAC2I,aAAa,CAAC;QAC5BzI,eAAe,CAACgH,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,MAAMtE,aAAa,CAACsE,aAAa,CAAC;;QAIlC;QACAzF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEwF,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAhG,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE+B,OAAO,CAAC;UAC1E,MAAMiE,WAAW,GAAG1K,WAAW,CAACsH,OAAO,CAAC4C,aAAa,EAAE,CAAC,EAAEzD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMiD,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhE5F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2C,MAAM,CAAC;UAC5C5C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2C,MAAM,GAAGA,MAAM,CAAC7C,MAAM,GAAG,CAAC,CAAC;UACzE,IAAI6C,MAAM,IAAIA,MAAM,CAAC7C,MAAM,GAAG,CAAC,EAAE;YAC/BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2C,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACL5C,OAAO,CAACwF,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAvI,aAAa,CAAC0F,MAAM,IAAI,EAAE,CAAC;;UAE3B;UACAjD,mBAAmB,CAACiD,MAAM,IAAI,EAAE,EAAEzF,SAAS,CAAC;QAC9C,CAAC,CAAC,OAAOgJ,SAAS,EAAE;UAClBnG,OAAO,CAACzC,KAAK,CAAC,yCAAyC,EAAE4I,SAAS,CAAC;UACnEnG,OAAO,CAACzC,KAAK,CAAC,8BAA8B,EAAE;YAC5CK,OAAO,EAAEuI,SAAS,CAACvI,OAAO;YAC1BwI,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAnJ,aAAa,CAAC,EAAE,CAAC;UACjB8C,OAAO,CAACwF,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACAhI,QAAQ,CAAC,2CAA2C2I,SAAS,CAACvI,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAoC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEwF,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAhG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAM0G,YAAY,GAAGpL,WAAW,CAACsH,OAAO,CAAC4C,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMxC,KAAK,GAAG,MAAM4C,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhE5F,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgD,KAAK,CAAC;UAC1CjD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgD,KAAK,GAAGA,KAAK,CAAClD,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIkD,KAAK,IAAIA,KAAK,CAAClD,MAAM,GAAG,CAAC,EAAE;YAC7BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgD,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLjD,OAAO,CAACwF,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACArI,YAAY,CAAC6F,KAAK,IAAI,EAAE,CAAC;;UAEzB;UACAtD,mBAAmB,CAAC1C,UAAU,EAAEgG,KAAK,IAAI,EAAE,CAAC;QAC9C,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBnD,OAAO,CAACzC,KAAK,CAAC,wCAAwC,EAAE4F,UAAU,CAAC;UACnEnD,OAAO,CAACzC,KAAK,CAAC,6BAA6B,EAAE;YAC3CK,OAAO,EAAEuF,UAAU,CAACvF,OAAO;YAC3BwI,MAAM,EAAEjD,UAAU,CAACiD,MAAM;YACzBC,IAAI,EAAElD,UAAU,CAACkD,IAAI;YACrBC,KAAK,EAAEnD,UAAU,CAACmD,KAAK;YACvBC,IAAI,EAAEpD,UAAU,CAACoD,IAAI;YACrBC,IAAI,EAAErD,UAAU,CAACqD,IAAI;YACrBC,QAAQ,EAAEtD,UAAU,CAACsD,QAAQ,GAAG;cAC9BL,MAAM,EAAEjD,UAAU,CAACsD,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEvD,UAAU,CAACsD,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAElD,UAAU,CAACsD,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAjJ,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0C2F,UAAU,CAACvF,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAN,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOsJ,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZlH,OAAO,CAACzC,KAAK,CAAC,kCAAkC,EAAEqJ,GAAG,CAAC;QACtD5G,OAAO,CAACzC,KAAK,CAAC,2BAA2B,EAAE;UACzCK,OAAO,EAAEgJ,GAAG,CAAChJ,OAAO;UACpBwI,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAChJ,OAAO,IAAIgJ,GAAG,CAAChJ,OAAO,CAAC6F,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE0D,YAAY,GAAGP,GAAG,CAAChJ,OAAO;QAC5B,CAAC,MAAM,IAAIgJ,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAChJ,OAAO,EAAE;UACtBuJ,YAAY,GAAGP,GAAG,CAAChJ,OAAO;QAC5B;QAEAJ,QAAQ,CAAC,gCAAgC2J,YAAY,sBAAsB,CAAC;;QAE5E;QACAjK,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDuG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMqF,iBAAiB,GAAIjH,IAAI,IAAK;IAClCrC,eAAe,CAACqC,IAAI,CAAC;IACrBnC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMqJ,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrJ,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMwJ,uBAAuB,GAAGA,CAAA,KAAM;IACpC7J,eAAe,CAAC8J,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7J,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACA,MAAM8J,gBAAgB,GAAGA,CAAC7J,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAM6J,qBAAqB,GAAGA,CAAA,KAAM;IAClCvJ,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;IACtC;IACA,IAAIA,gBAAgB,EAAE;MACpBG,qBAAqB,CAAC,EAAE,CAAC;MACzBE,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAMoJ,+BAA+B,GAAIC,WAAW,IAAK;IACvDvJ,qBAAqB,CAACuJ,WAAW,CAAC;EACpC,CAAC;EAED,MAAMC,8BAA8B,GAAID,WAAW,IAAK;IACtDrJ,oBAAoB,CAACqJ,WAAW,CAAC;EACnC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,kBAAkB,GAAG9K,UAAU,CAACkD,MAAM,CAACC,IAAI,IAAIhC,kBAAkB,CAACqF,QAAQ,CAACrD,IAAI,CAAC4H,OAAO,CAAC,CAAC;IAC/F,MAAMC,iBAAiB,GAAG9K,SAAS,CAACgD,MAAM,CAACC,IAAI,IAAI9B,iBAAiB,CAACmF,QAAQ,CAACrD,IAAI,CAAC4H,OAAO,CAAC,CAAC;IAC5F,OAAO,CAAC,GAAGD,kBAAkB,EAAE,GAAGE,iBAAiB,CAAC;EACtD,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAO9J,kBAAkB,CAAC2B,MAAM,GAAGzB,iBAAiB,CAACyB,MAAM;EAC7D,CAAC;;EAED;EACA,MAAMoI,uBAAuB,GAAGA,CAAC/H,IAAI,EAAEgI,MAAM,KAAK;IAChDpI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmI,MAAM,EAAE,WAAW,EAAEhI,IAAI,CAAC;IAElE,QAAQgI,MAAM;MACZ,KAAK,cAAc;QACjBf,iBAAiB,CAACjH,IAAI,CAAC;QACvB;MACF,KAAK,MAAM;QACT;QACAqH,gBAAgB,CAAC,iBAAiBrH,IAAI,CAAC4H,OAAO,6BAA6B,EAAE,MAAM,CAAC;QACpF;MACF,KAAK,QAAQ;QACX;QACA,IAAItE,MAAM,CAAC2E,OAAO,CAAC,yCAAyCjI,IAAI,CAAC4H,OAAO,GAAG,CAAC,EAAE;UAC5EP,gBAAgB,CAAC,qBAAqBrH,IAAI,CAAC4H,OAAO,6BAA6B,EAAE,SAAS,CAAC;QAC7F;QACA;MACF,KAAK,QAAQ;QACX,IAAI/K,UAAU,CAACqL,IAAI,CAACtD,CAAC,IAAIA,CAAC,CAACgD,OAAO,KAAK5H,IAAI,CAAC4H,OAAO,CAAC,EAAE;UACpD;UACA,MAAMO,UAAU,GAAGnK,kBAAkB,CAACqF,QAAQ,CAACrD,IAAI,CAAC4H,OAAO,CAAC;UAC5D,IAAIO,UAAU,EAAE;YACdlK,qBAAqB,CAACmJ,IAAI,IAAIA,IAAI,CAACrH,MAAM,CAACqI,EAAE,IAAIA,EAAE,KAAKpI,IAAI,CAAC4H,OAAO,CAAC,CAAC;UACvE,CAAC,MAAM;YACL3J,qBAAqB,CAACmJ,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEpH,IAAI,CAAC4H,OAAO,CAAC,CAAC;UACxD;QACF,CAAC,MAAM;UACL;UACA,MAAMO,UAAU,GAAGjK,iBAAiB,CAACmF,QAAQ,CAACrD,IAAI,CAAC4H,OAAO,CAAC;UAC3D,IAAIO,UAAU,EAAE;YACdhK,oBAAoB,CAACiJ,IAAI,IAAIA,IAAI,CAACrH,MAAM,CAACqI,EAAE,IAAIA,EAAE,KAAKpI,IAAI,CAAC4H,OAAO,CAAC,CAAC;UACtE,CAAC,MAAM;YACLzJ,oBAAoB,CAACiJ,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEpH,IAAI,CAAC4H,OAAO,CAAC,CAAC;UACvD;QACF;QACA;QACA,IAAI,CAAC9J,gBAAgB,EAAE;UACrBC,mBAAmB,CAAC,IAAI,CAAC;QAC3B;QACA;MACF,KAAK,SAAS;QACZsK,SAAS,CAACC,SAAS,CAACC,SAAS,CAACvI,IAAI,CAAC4H,OAAO,CAAC;QAC3CP,gBAAgB,CAAC,WAAWrH,IAAI,CAAC4H,OAAO,wBAAwB,EAAE,SAAS,CAAC;QAC5E;MACF,KAAK,cAAc;QACjB,MAAMY,OAAO,GAAG,OAAOxI,IAAI,CAAC4H,OAAO,gBAAgB5H,IAAI,CAAC8B,SAAS,cAAc9B,IAAI,CAACyI,OAAO,YAAYzI,IAAI,CAACU,aAAa,EAAE;QAC3H2H,SAAS,CAACC,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC;QACtCnB,gBAAgB,CAAC,qCAAqC,EAAE,SAAS,CAAC;QAClE;MACF;QACEzH,OAAO,CAACwF,IAAI,CAAC,0BAA0B,EAAE4C,MAAM,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMU,mBAAmB,GAAI1I,IAAI,IAAK;IACpC,MAAMmI,UAAU,GAAGtL,UAAU,CAACqL,IAAI,CAACtD,CAAC,IAAIA,CAAC,CAACgD,OAAO,MAAK5H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4H,OAAO,EAAC,GAChE5J,kBAAkB,CAACqF,QAAQ,CAACrD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4H,OAAO,CAAC,GAC1C1J,iBAAiB,CAACmF,QAAQ,CAACrD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4H,OAAO,CAAC;IAE7C,OAAO,CACL;MACEe,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,QAAQ,CAAA5I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4H,OAAO,KAAI,EAAE;IACpC,CAAC,EACD;MACEQ,EAAE,EAAE,cAAc;MAClBQ,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAEnN,OAAA,CAACtB,cAAc;QAAC0O,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzClB,MAAM,EAAE,cAAc;MACtBmB,OAAO,EAAEpB;IACX,CAAC,EACD;MACEY,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,MAAM;MACVQ,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAEnN,OAAA,CAACpB,QAAQ;QAACwO,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClB,MAAM,EAAE,MAAM;MACdmB,OAAO,EAAEpB,uBAAuB;MAChCqB,KAAK,EAAE;IACT,CAAC,EACD;MACEhB,EAAE,EAAE,QAAQ;MACZQ,KAAK,EAAE,SAAS;MAChBC,IAAI,eAAEnN,OAAA,CAAClB,UAAU;QAACsO,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrClB,MAAM,EAAE,QAAQ;MAChBmB,OAAO,EAAEpB,uBAAuB;MAChCqB,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZQ,KAAK,EAAET,UAAU,GAAG,aAAa,GAAG,WAAW;MAC/CU,IAAI,eAAEnN,OAAA,CAAChB,aAAa;QAACoO,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxClB,MAAM,EAAE,QAAQ;MAChBmB,OAAO,EAAEpB,uBAAuB;MAChCqB,KAAK,EAAEjB,UAAU,GAAG,SAAS,GAAG;IAClC,CAAC,EACD;MACEQ,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,SAAS;MACbQ,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAEnN,OAAA,CAACd,QAAQ;QAACkO,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClB,MAAM,EAAE,SAAS;MACjBmB,OAAO,EAAEpB,uBAAuB;MAChCsB,QAAQ,EAAE;IACZ,CAAC,EACD;MACEjB,EAAE,EAAE,cAAc;MAClBQ,KAAK,EAAE,gBAAgB;MACvBC,IAAI,eAAEnN,OAAA,CAACd,QAAQ;QAACkO,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClB,MAAM,EAAE,cAAc;MACtBmB,OAAO,EAAEpB,uBAAuB;MAChCuB,WAAW,EAAE;IACf,CAAC,CACF;EACH,CAAC;;EAED;;EAEA;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtB7N,OAAA,CAAC5D,KAAK;IAAC0R,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7ClO,OAAA,CAACzC,KAAK;MAAC4Q,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnGlO,OAAA,CAACzC,KAAK;QAAC4Q,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlO,OAAA,CAACtC,SAAS;UAACgQ,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CxN,OAAA,CAAC9D,GAAG;UAAAgS,QAAA,gBACFlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DxL,UAAU,CAACE;UAAU;YAAAyK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbxN,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,SAAS;YAACd,KAAK,EAAC,gBAAgB;YAAAQ,QAAA,EAAC;UAErD;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERxN,OAAA,CAACzC,KAAK;QAAC4Q,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlO,OAAA,CAACpC,eAAe;UAAC8P,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDxN,OAAA,CAAC9D,GAAG;UAAAgS,QAAA,gBACFlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DxL,UAAU,CAACG;UAAc;YAAAwK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACbxN,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,SAAS;YAACd,KAAK,EAAC,gBAAgB;YAAAQ,QAAA,EAAC;UAErD;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERxN,OAAA,CAACzC,KAAK;QAAC4Q,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlO,OAAA,CAAChC,QAAQ;UAAC0P,KAAK,EAAC,MAAM;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CxN,OAAA,CAAC9D,GAAG;UAAAgS,QAAA,gBACFlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DxL,UAAU,CAACM;UAAa;YAAAqK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACbxN,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,SAAS;YAACd,KAAK,EAAC,gBAAgB;YAAAQ,QAAA,EAAC;UAErD;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERxN,OAAA,CAACzC,KAAK;QAAC4Q,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlO,OAAA,CAAC9D,GAAG;UAAC4R,EAAE,EAAE;YACPa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBZ,OAAO,EAAEvL,UAAU,CAACQ,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAC1DR,UAAU,CAACQ,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAClF4L,OAAO,EAAE,MAAM;YACfT,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACAlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAACf,KAAK,EAAC,OAAO;YAAAQ,QAAA,GAC1DxL,UAAU,CAACQ,wBAAwB,EAAC,GACvC;UAAA;YAAAmK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNxN,OAAA,CAAC9D,GAAG;UAAAgS,QAAA,gBACFlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEvE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxN,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,SAAS;YAACd,KAAK,EAAC,gBAAgB;YAAAQ,QAAA,GACjDxL,UAAU,CAACW,eAAe,EAAC,cAC9B;UAAA;YAAAgK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;;EAEA;;EAEA;EACA,MAAMuB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC/M,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEhC,OAAA,CAAClD,MAAM;MAAC+E,IAAI,EAAEK,iBAAkB;MAAC8M,OAAO,EAAExD,kBAAmB;MAACyD,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAhB,QAAA,gBACnFlO,OAAA,CAACjD,WAAW;QAAAmR,QAAA,GAAC,iBACI,EAAClM,YAAY,CAACkK,OAAO;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdxN,OAAA,CAAChD,aAAa;QAACmS,QAAQ;QAAAjB,QAAA,eACrBlO,OAAA,CAAC1D,IAAI;UAAC8S,SAAS;UAAChB,OAAO,EAAE,CAAE;UAAAF,QAAA,gBACzBlO,OAAA,CAAC1D,IAAI;YAAC+S,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvBlO,OAAA,CAAC7D,UAAU;cAACqS,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAqB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ExN,OAAA,CAAC9D,GAAG;cAAC4R,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBlO,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAQ;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACyN,OAAO,IAAI,KAAK;cAAA;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAQ;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC0N,OAAO,IAAI,KAAK;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAU;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACoE,SAAS,IAAI,KAAK;cAAA;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC2N,WAAW,IAAI,KAAK;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAErGxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAW;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC+K,OAAO,IAAI,KAAK;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElG,CAAC,eAENxN,OAAA,CAAC7D,UAAU;cAACqS,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAQ;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClExN,OAAA,CAAC9D,GAAG;cAAC4R,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBlO,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAW;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC4N,mBAAmB,IAAI,KAAK;cAAA;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC6N,eAAe,IAAI,KAAK;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAY;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC8N,2BAA2B,IAAI,KAAK;cAAA;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAa;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACyC,qBAAqB,IAAI,KAAK;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAQ;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC+N,gBAAgB,IAAI,KAAK;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPxN,OAAA,CAAC1D,IAAI;YAAC+S,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvBlO,OAAA,CAAC7D,UAAU;cAACqS,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChExN,OAAA,CAAC9D,GAAG;cAAC4R,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBlO,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAW;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACgO,iBAAiB,IAAI,KAAK;cAAA;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACiO,aAAa,IAAI,KAAK;cAAA;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAY;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACkO,yBAAyB,IAAI,KAAK;cAAA;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAa;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAAC0C,mBAAmB,IAAI,KAAK;cAAA;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAQ;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACmO,cAAc,IAAI,KAAK;cAAA;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENxN,OAAA,CAAC7D,UAAU;cAACqS,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAa;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvExN,OAAA,CAAC9D,GAAG;cAAC4R,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBlO,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAc;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACgD,aAAa,IAAI,KAAK;cAAA;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACiD,eAAe,IAAI,GAAG;cAAA;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAM;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7N,2BAA2B,CAACqC,YAAY,CAACuC,mBAAmB,CAAC;cAAA;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAa;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACwC,YAAY,IAAI,GAAG;cAAA;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACoO,SAAS,IAAI,KAAK;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAkB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACqO,iBAAiB,IAAI,KAAK;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAa;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxL,YAAY,CAACsO,YAAY,IAAI,KAAK;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GxN,OAAA,CAAC7D,UAAU;gBAACqS,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAClO,OAAA;kBAAAkO,QAAA,EAAQ;gBAAqB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI+C,IAAI,CAACvO,YAAY,CAACwO,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBxN,OAAA,CAAC/C,aAAa;QAAAiR,QAAA,eACZlO,OAAA,CAAC3D,MAAM;UAACoR,OAAO,EAAEjC,kBAAmB;UAAA0C,QAAA,EAAC;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAIA,oBACExN,OAAA,CAAC9D,GAAG;IAACwU,SAAS,EAAC,WAAW;IAAAxC,QAAA,EACvB3M,OAAO,gBACNvB,OAAA,CAAC9D,GAAG;MAAC4R,EAAE,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAE6B,aAAa,EAAE,QAAQ;QAAEtC,UAAU,EAAE,QAAQ;QAAEuC,EAAE,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACjFlO,OAAA,CAACpD,gBAAgB;QAACiU,IAAI,EAAE;MAAG;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BxN,OAAA,CAAC7D,UAAU;QAAC2R,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,EAAC;MAAmB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DxN,OAAA,CAAC3D,MAAM;QACLmS,OAAO,EAAC,UAAU;QAClBd,KAAK,EAAC,SAAS;QACfD,OAAO,EAAEA,CAAA,KAAM7F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCgG,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,EACf;MAED;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ/L,KAAK,gBACPzB,OAAA,CAAC9D,GAAG;MAAAgS,QAAA,gBACFlO,OAAA,CAACvD,KAAK;QAACsF,QAAQ,EAAC,OAAO;QAAC+L,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GACnCzM,KAAK,EACLA,KAAK,CAACkG,QAAQ,CAAC,eAAe,CAAC,iBAC9B3H,OAAA,CAAC7D,UAAU;UAACqS,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACxClO,OAAA;YAAAkO,QAAA,EAAQ;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAxN,OAAA;YAAAqN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAxN,OAAA;YAAAkO,QAAA,EAAM;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRxN,OAAA,CAAC9D,GAAG;QAAC4R,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEgC,GAAG,EAAE;QAAE,CAAE;QAAA5C,QAAA,eACnClO,OAAA,CAAC3D,MAAM;UACLmS,OAAO,EAAC,WAAW;UACnBkC,SAAS,EAAC,gBAAgB;UAC1BjD,OAAO,EAAEA,CAAA,KAAM7F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAoG,QAAA,EACzC;QAED;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENxN,OAAA,CAAC9D,GAAG;MAAAgS,QAAA,GAED3K,oBAAoB,CAACU,MAAM,GAAG,CAAC,iBAC9BjE,OAAA,CAAC5D,KAAK;QAAC0R,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,eACzBlO,OAAA,CAAC9D,GAAG;UAAC4R,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAET,UAAU,EAAE,QAAQ;YAAEyC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzDlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAAgB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtDxN,OAAA,CAAC7C,WAAW;YAAC0T,IAAI,EAAC,OAAO;YAAC/C,EAAE,EAAE;cAAEiD,QAAQ,EAAE;YAAI,CAAE;YAAA7C,QAAA,gBAC9ClO,OAAA,CAAC5C,UAAU;cAAA8Q,QAAA,EAAC;YAAyB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDxN,OAAA,CAAC3C,MAAM;cACL4I,KAAK,EAAExC,oBAAoB,IAAI,UAAW;cAC1CuN,QAAQ,EAAEnL,qBAAsB;cAChCqH,KAAK,EAAC,2BAA2B;cAAAgB,QAAA,gBAEjClO,OAAA,CAAC1C,QAAQ;gBAAC2I,KAAK,EAAC,UAAU;gBAAAiI,QAAA,GAAC,kCACH,EAACvK,iBAAiB,IAAI,IAAIA,iBAAiB,GAAG;cAAA;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,EACVjK,oBAAoB,CAAC0F,GAAG,CAAEgI,GAAG,iBAC5BjR,OAAA,CAAC1C,QAAQ;gBAAqB2I,KAAK,EAAEgL,GAAG,CAACC,SAAU;gBAAAhD,QAAA,GAAC,eAC/C,EAAC+C,GAAG,CAACC,SAAS,EAAC,IAAE,EAACD,GAAG,CAACE,UAAU,EAAC,QACpC,EAACF,GAAG,CAACC,SAAS,KAAKvN,iBAAiB,IAAI,YAAY;cAAA,GAFvCsN,GAAG,CAACC,SAAS;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACdxN,OAAA,CAACrD,IAAI;YACHuQ,KAAK,EACHzJ,oBAAoB,GAChB,YAAYA,oBAAoB,EAAE,GAClC,aAAaE,iBAAiB,IAAI,KAAK,EAC5C;YACD+J,KAAK,EAAEjK,oBAAoB,GAAG,WAAW,GAAG,SAAU;YACtD+K,OAAO,EAAC;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGAK,eAAe,CAAC,CAAC,eAGlB7N,OAAA,CAAC9D,GAAG;QAAC4R,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACjBlO,OAAA,CAAC9D,GAAG;UAAC4R,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAER,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE,QAAQ;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,gBACzFlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,IAAI;YAAAN,QAAA,GAAC,cACX,EAAC/M,UAAU,CAAC8C,MAAM,GAAG,CAAC,GAAG,IAAI9C,UAAU,CAAC8C,MAAM,GAAG,GAAG,EAAE;UAAA;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAEbxN,OAAA,CAAC9D,GAAG;YAAC4R,EAAE,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEgC,GAAG,EAAE,CAAC;cAAEzC,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,GAExD9L,gBAAgB,IAAIgK,qBAAqB,CAAC,CAAC,GAAG,CAAC,iBAC9CpM,OAAA,CAACrD,IAAI;cACHuQ,KAAK,EAAE,GAAGd,qBAAqB,CAAC,CAAC,mBAAoB;cACrDsB,KAAK,EAAC,SAAS;cACfc,OAAO,EAAC;YAAU;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACF,eAGDxN,OAAA,CAAC3D,MAAM;cACLmS,OAAO,EAAEpM,gBAAgB,GAAG,WAAW,GAAG,UAAW;cACrDsL,KAAK,EAAC,SAAS;cACfD,OAAO,EAAE7B,qBAAsB;cAC/BwF,SAAS,EAAEhP,gBAAgB,gBAAGpC,OAAA,CAAC1B,YAAY;gBAAA+O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxN,OAAA,CAACxB,wBAAwB;gBAAA6O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAU,QAAA,EAE7E9L,gBAAgB,GAAG,sBAAsB,GAAG;YAAmB;cAAAiL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL6D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIpQ,UAAU,CAAC8C,MAAM,GAAG,CAAC,iBAC9DjE,OAAA,CAAC9D,GAAG;UAAC4R,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,SAAS;YAAEY,YAAY,EAAE,CAAC;YAAEzB,QAAQ,EAAE,QAAQ;YAAEoE,UAAU,EAAE,WAAW;YAAE1C,OAAO,EAAE;UAAO,CAAE;UAAAZ,QAAA,EACzHuD,MAAM,CAACC,IAAI,CAACvQ,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC8H,GAAG,CAACb,GAAG,iBACjCpI,OAAA;YAAAkO,QAAA,GAAgB9F,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACoI,SAAS,CAACxQ,UAAU,CAAC,CAAC,CAAC,CAACiH,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDxN,OAAA,CAACJ,mBAAmB;UAClBgS,IAAI,EAAEzQ,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjBsQ,oBAAoB,EAAGC,YAAY,IAAK5N,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2N,YAAY,CAAC7N,MAAM,CAAE;UAClGN,iBAAiB,EAAE,EAAAxD,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAe4R,mBAAmB,OAAA3R,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAe8Q,SAAS,OAAA7Q,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAe4Q,GAAG,CAAC;UACxG7O,gBAAgB,EAAEA,gBAAiB;UACnC4P,YAAY,EAAE1P,kBAAmB;UACjC2P,iBAAiB,EAAEpG,+BAAgC;UACnDqG,gBAAgB,EAAElF,mBAAoB;UACtCmF,mBAAmB,EAAE9F;QAAwB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,EACDrM,UAAU,CAAC8C,MAAM,KAAK,CAAC,IAAI,CAAC1C,OAAO,iBAClCvB,OAAA,CAACvD,KAAK;UAACsF,QAAQ,EAAC,MAAM;UAAC+L,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,EAAC;QAEtC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxN,OAAA,CAAC9D,GAAG;QAAC4R,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACjBlO,OAAA,CAAC9D,GAAG;UAAC4R,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAER,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE,QAAQ;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACzFlO,OAAA,CAAC7D,UAAU;YAACqS,OAAO,EAAC,IAAI;YAAAN,QAAA,GAAC,aACZ,EAAC7M,SAAS,CAAC4C,MAAM,GAAG,CAAC,GAAG,IAAI5C,SAAS,CAAC4C,MAAM,GAAG,GAAG,EAAE;UAAA;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNxN,OAAA,CAACJ,mBAAmB;UAClBgS,IAAI,EAAEvQ,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjBsQ,oBAAoB,EAAGC,YAAY,IAAK5N,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE2N,YAAY,CAAC7N,MAAM,CAAE;UACjG7B,gBAAgB,EAAEA,gBAAiB;UACnC4P,YAAY,EAAExP,iBAAkB;UAChCyP,iBAAiB,EAAElG,8BAA+B;UAClDmG,gBAAgB,EAAElF,mBAAoB;UACtCmF,mBAAmB,EAAE9F;QAAwB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,EACDnM,SAAS,CAAC4C,MAAM,KAAK,CAAC,IAAI,CAAC1C,OAAO,iBACjCvB,OAAA,CAACvD,KAAK;UAACsF,QAAQ,EAAC,MAAM;UAAC+L,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,EAAC;QAEtC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLuB,mBAAmB,CAAC,CAAC,eAGtB/O,OAAA,CAAClD,MAAM;QACL+E,IAAI,EAAErB,qBAAsB;QAC5BwO,OAAO,EAAEA,CAAA,KAAMvO,wBAAwB,CAAC,KAAK,CAAE;QAC/CyO,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAf,QAAA,eAEblO,OAAA,CAACR,oBAAoB;UACnBuB,UAAU,EAAEA,UAAW;UACvBqR,SAAS,EAAGtQ,OAAO,IAAK;YACtB;YACArB,wBAAwB,CAAC,KAAK,CAAC;;YAE/B;YACA,IAAIqB,OAAO,EAAE;cACX;cACAoC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAErC,OAAO,CAAC;cAC9C;cACA6J,gBAAgB,CAAC7J,OAAO,EAAE,SAAS,CAAC;cACpC;cACAyF,UAAU,CAAC,MAAM;gBACfrD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAuC,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAOjF,KAAK,EAAE;kBACdyC,OAAO,CAACzC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAmG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFkO,OAAO,EAAGvQ,OAAO,IAAK;YACpB;YACAoC,OAAO,CAACzC,KAAK,CAAC,0CAA0C,EAAEK,OAAO,CAAC;YAClE;YACAwQ,KAAK,CAAC,WAAWxQ,OAAO,EAAE,CAAC;YAC3B;YACArB,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACAiG,SAAS,CAAC,CAAC;UACb,CAAE;UACF6L,aAAa,EAAC;QAAa;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAIR9M,sBAAsB,IAAIwD,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEpD,UAAU,CAAC,eAEhHf,OAAA,CAAClD,MAAM;QACL+E,IAAI,EAAEnB,sBAAuB;QAC7BsO,OAAO,EAAEA,CAAA,KAAMrO,yBAAyB,CAAC,KAAK,CAAE;QAChDuO,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAf,QAAA,eAEblO,OAAA,CAACR,oBAAoB;UACnBuB,UAAU,EAAEA,UAAW;UACvBqR,SAAS,EAAGtQ,OAAO,IAAK;YACtB;YACAnB,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAImB,OAAO,EAAE;cACX;cACAoC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAErC,OAAO,CAAC;cAC9C;cACA6J,gBAAgB,CAAC7J,OAAO,EAAE,SAAS,CAAC;cACpC;cACAoC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACpD;cACAoD,UAAU,CAAC,MAAM;gBACf,IAAI;kBACFb,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAOjF,KAAK,EAAE;kBACdyC,OAAO,CAACzC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAmG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFkO,OAAO,EAAGvQ,OAAO,IAAK;YACpB;YACAoC,OAAO,CAACzC,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;YAC9D;YACAwQ,KAAK,CAAC,WAAWxQ,OAAO,EAAE,CAAC;YAC3B;YACAnB,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACAuD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDuC,SAAS,CAAC,IAAI,CAAC;UACjB,CAAE;UACF6L,aAAa,EAAC;QAAc;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTxN,OAAA,CAAClD,MAAM;QAAC+E,IAAI,EAAEjB,sBAAuB;QAACoO,OAAO,EAAEA,CAAA,KAAMnO,yBAAyB,CAAC,KAAK,CAAE;QAACoO,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAhB,QAAA,eAC5GlO,OAAA,CAACR,oBAAoB;UACnBuB,UAAU,EAAEA,UAAW;UACvBqR,SAAS,EAAGtQ,OAAO,IAAK;YACtB;YACAjB,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAIiB,OAAO,EAAE;cACX;cACAoC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAErC,OAAO,CAAC;cAC9C;cACA6J,gBAAgB,CAAC7J,OAAO,EAAE,SAAS,CAAC;cACpC;cACAyF,UAAU,CAAC,MAAM;gBACfrD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAuC,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAOjF,KAAK,EAAE;kBACdyC,OAAO,CAACzC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAyC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;kBACzDyD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFkO,OAAO,EAAGvQ,OAAO,IAAK;YACpB;YACAoC,OAAO,CAACzC,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;YAC9D;YACA6J,gBAAgB,CAAC,WAAW7J,OAAO,EAAE,EAAE,OAAO,CAAC;YAC/C;YACAjB,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACA6F,SAAS,CAAC,IAAI,CAAC;UACjB,CAAE;UACF6L,aAAa,EAAC;QAAc;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTxN,OAAA,CAAC9C,QAAQ;QACP2E,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxB2Q,gBAAgB,EAAE,IAAK;QACvBxD,OAAO,EAAEvD,uBAAwB;QACjCgH,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAzE,QAAA,eAE3DlO,OAAA,CAACvD,KAAK;UAACuS,OAAO,EAAEvD,uBAAwB;UAAC1J,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAAC+L,EAAE,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAC7FvM,YAAY,CAACG;QAAO;UAAAuL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtN,EAAA,CAlqCID,kBAAkB;EAAA,QACYX,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAAuT,EAAA,GAHxB3S,kBAAkB;AAoqCxB,eAAeA,kBAAkB;AAAC,IAAA2S,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}