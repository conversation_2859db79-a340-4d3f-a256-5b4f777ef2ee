{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ModificaBobinaDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, TextField, Button, Box, Typography, Alert, Chip, Grid, Autocomplete, FormControl, InputLabel, Select, MenuItem } from '@mui/material';\nimport { Settings as SettingsIcon, Cable as CableIcon, FiberManualRecord as BobinaIcon, SwapHoriz as SwapIcon } from '@mui/icons-material';\n\n/**\n * Dialogo per modificare la bobina associata a un cavo\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Dati del cavo selezionato\n * @param {Array} props.bobineDisponibili - Lista delle bobine disponibili\n * @param {Function} props.onSave - Funzione chiamata quando si salva la modifica\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModificaBobinaDialog = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  bobineDisponibili = [],\n  onSave = () => {},\n  loading = false\n}) => {\n  _s();\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [motivazione, setMotivazione] = useState('');\n  const [error, setError] = useState('');\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      // Trova la bobina attualmente associata\n      const bobinaAttuale = bobineDisponibili.find(b => b.id_bobina === cavo.id_bobina);\n      setSelectedBobina(bobinaAttuale || null);\n      setMotivazione('');\n      setError('');\n    }\n  }, [open, cavo, bobineDisponibili]);\n  const handleSave = () => {\n    if (!selectedBobina) {\n      setError('Selezionare una bobina');\n      return;\n    }\n    if (!motivazione.trim()) {\n      setError('Inserire una motivazione per la modifica');\n      return;\n    }\n\n    // Verifica compatibilità\n    if (selectedBobina.tipologia !== cavo.tipologia || selectedBobina.sezione !== cavo.sezione) {\n      setError('La bobina selezionata non è compatibile con il cavo (tipologia/sezione diverse)');\n      return;\n    }\n    setError('');\n    onSave(cavo.id_cavo, selectedBobina.id_bobina, motivazione);\n  };\n  const handleClose = () => {\n    if (!loading) {\n      setError('');\n      onClose();\n    }\n  };\n  if (!cavo) return null;\n\n  // Filtra bobine compatibili\n  const bobineCompatibili = bobineDisponibili.filter(bobina => bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione);\n  const bobinaAttuale = bobineDisponibili.find(b => b.id_bobina === cavo.id_bobina);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        borderRadius: 2,\n        boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        pb: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Modifica Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                backgroundColor: 'grey.50',\n                borderRadius: 1,\n                border: '1px solid',\n                borderColor: 'grey.200'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                  fontSize: \"small\",\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: \"bold\",\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"INSTALLATO\",\n                  size: \"small\",\n                  color: \"success\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), \" \", cavo.tipologia, \" | \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 66\n                }, this), \" \", cavo.sezione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this), \" \", cavo.metratura_reale || 0, \"m | \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina attuale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 81\n                }, this), \" \", cavo.id_bobina || 'N/D']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), bobinaAttuale && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Bobina Attuale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              backgroundColor: 'primary.50',\n              borderRadius: 1,\n              border: '1px solid',\n              borderColor: 'primary.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(BobinaIcon, {\n                fontSize: \"small\",\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                fontWeight: \"bold\",\n                children: bobinaAttuale.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"- \", bobinaAttuale.metri_residui || 0, \"m residui\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n          value: selectedBobina,\n          onChange: (event, newValue) => setSelectedBobina(newValue),\n          options: bobineCompatibili,\n          getOptionLabel: option => `${option.id_bobina} (${option.metri_residui || 0}m residui)`,\n          renderOption: (props, option) => /*#__PURE__*/_jsxDEV(Box, {\n            component: \"li\",\n            ...props,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1,\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(BobinaIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flexGrow: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: option.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: [option.tipologia, \" \", option.sezione, \" - \", option.metri_residui || 0, \"m residui\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), option.id_bobina === cavo.id_bobina && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Attuale\",\n                size: \"small\",\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this),\n          renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n            ...params,\n            label: \"Seleziona Bobina\",\n            error: Boolean(error && !selectedBobina),\n            helperText: `${bobineCompatibili.length} bobine compatibili disponibili`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this),\n          sx: {\n            mb: 2\n          },\n          disabled: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Motivazione della modifica\",\n          multiline: true,\n          rows: 3,\n          fullWidth: true,\n          value: motivazione,\n          onChange: e => setMotivazione(e.target.value),\n          error: Boolean(error && !motivazione.trim()),\n          helperText: error || \"Specificare il motivo della modifica (es. bobina danneggiata, ottimizzazione, ecc.)\",\n          disabled: loading,\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), selectedBobina && selectedBobina.id_bobina !== cavo.id_bobina && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          icon: /*#__PURE__*/_jsxDEV(SwapIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 21\n          }, this),\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Cambio bobina:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this), \" da \", cavo.id_bobina || 'N/D', \" a \", selectedBobina.id_bobina]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Questa operazione aggiorner\\xE0 i metri residui delle bobine coinvolte.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), bobineCompatibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessuna bobina compatibile disponibile per questo tipo di cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        px: 3,\n        pb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        disabled: loading,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        variant: \"contained\",\n        disabled: loading || !selectedBobina || !motivazione.trim() || selectedBobina.id_bobina === cavo.id_bobina,\n        startIcon: loading ? null : /*#__PURE__*/_jsxDEV(SwapIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 39\n        }, this),\n        children: loading ? 'Salvando...' : 'Modifica Bobina'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(ModificaBobinaDialog, \"eYQ5Fo3gCL8FCb9MCoQRrmzwMNA=\");\n_c = ModificaBobinaDialog;\nexport default ModificaBobinaDialog;\nvar _c;\n$RefreshReg$(_c, \"ModificaBobinaDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "Box", "Typography", "<PERSON><PERSON>", "Chip", "Grid", "Autocomplete", "FormControl", "InputLabel", "Select", "MenuItem", "Settings", "SettingsIcon", "Cable", "CableIcon", "FiberManualRecord", "BobinaIcon", "SwapHoriz", "SwapIcon", "jsxDEV", "_jsxDEV", "ModificaBobinaDialog", "open", "onClose", "cavo", "bobine<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onSave", "loading", "_s", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "motivazione", "setMotivazione", "error", "setError", "bobinaAtt<PERSON>e", "find", "b", "id_bobina", "handleSave", "trim", "tipologia", "sezione", "id_cavo", "handleClose", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "borderRadius", "boxShadow", "children", "pb", "display", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mb", "container", "spacing", "item", "xs", "p", "backgroundColor", "border", "borderColor", "fontSize", "fontWeight", "label", "size", "metratura_reale", "gutterBottom", "metri_residui", "value", "onChange", "event", "newValue", "options", "getOptionLabel", "option", "renderOption", "props", "component", "width", "flexGrow", "renderInput", "params", "Boolean", "helperText", "length", "disabled", "multiline", "rows", "e", "target", "severity", "icon", "mt", "px", "onClick", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/ModificaBobinaDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Button,\n  Box,\n  Typography,\n  Alert,\n  Chip,\n  Grid,\n  Autocomplete,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem\n} from '@mui/material';\nimport {\n  Settings as SettingsIcon,\n  Cable as CableIcon,\n  FiberManualRecord as BobinaIcon,\n  SwapHoriz as SwapIcon\n} from '@mui/icons-material';\n\n/**\n * Dialogo per modificare la bobina associata a un cavo\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Dati del cavo selezionato\n * @param {Array} props.bobineDisponibili - Lista delle bobine disponibili\n * @param {Function} props.onSave - Funzione chiamata quando si salva la modifica\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nconst ModificaBobinaDialog = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  bobineDisponibili = [],\n  onSave = () => {},\n  loading = false\n}) => {\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [motivazione, setMotivazione] = useState('');\n  const [error, setError] = useState('');\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      // Trova la bobina attualmente associata\n      const bobinaAttuale = bobineDisponibili.find(b => b.id_bobina === cavo.id_bobina);\n      setSelectedBobina(bobinaAttuale || null);\n      setMotivazione('');\n      setError('');\n    }\n  }, [open, cavo, bobineDisponibili]);\n\n  const handleSave = () => {\n    if (!selectedBobina) {\n      setError('Selezionare una bobina');\n      return;\n    }\n\n    if (!motivazione.trim()) {\n      setError('Inserire una motivazione per la modifica');\n      return;\n    }\n\n    // Verifica compatibilità\n    if (selectedBobina.tipologia !== cavo.tipologia || selectedBobina.sezione !== cavo.sezione) {\n      setError('La bobina selezionata non è compatibile con il cavo (tipologia/sezione diverse)');\n      return;\n    }\n\n    setError('');\n    onSave(cavo.id_cavo, selectedBobina.id_bobina, motivazione);\n  };\n\n  const handleClose = () => {\n    if (!loading) {\n      setError('');\n      onClose();\n    }\n  };\n\n  if (!cavo) return null;\n\n  // Filtra bobine compatibili\n  const bobineCompatibili = bobineDisponibili.filter(bobina => \n    bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n  );\n\n  const bobinaAttuale = bobineDisponibili.find(b => b.id_bobina === cavo.id_bobina);\n\n  return (\n    <Dialog \n      open={open} \n      onClose={handleClose}\n      maxWidth=\"md\"\n      fullWidth\n      PaperProps={{\n        sx: {\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        }\n      }}\n    >\n      <DialogTitle sx={{ pb: 1 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <SettingsIcon color=\"primary\" />\n          <Typography variant=\"h6\">\n            Modifica Bobina\n          </Typography>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent>\n        <Box sx={{ mb: 3 }}>\n          {/* Informazioni cavo */}\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={12}>\n              <Box sx={{ \n                p: 2, \n                backgroundColor: 'grey.50', \n                borderRadius: 1,\n                border: '1px solid',\n                borderColor: 'grey.200'\n              }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>\n                  <CableIcon fontSize=\"small\" color=\"primary\" />\n                  <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                    {cavo.id_cavo}\n                  </Typography>\n                  <Chip label=\"INSTALLATO\" size=\"small\" color=\"success\" />\n                </Box>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  <strong>Tipologia:</strong> {cavo.tipologia} | <strong>Sezione:</strong> {cavo.sezione}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  <strong>Metri posati:</strong> {cavo.metratura_reale || 0}m | <strong>Bobina attuale:</strong> {cavo.id_bobina || 'N/D'}\n                </Typography>\n              </Box>\n            </Grid>\n          </Grid>\n\n          {/* Bobina attuale */}\n          {bobinaAttuale && (\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Bobina Attuale\n              </Typography>\n              <Box sx={{ \n                p: 2, \n                backgroundColor: 'primary.50', \n                borderRadius: 1,\n                border: '1px solid',\n                borderColor: 'primary.200'\n              }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                  <BobinaIcon fontSize=\"small\" color=\"primary\" />\n                  <Typography variant=\"body1\" fontWeight=\"bold\">\n                    {bobinaAttuale.id_bobina}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    - {bobinaAttuale.metri_residui || 0}m residui\n                  </Typography>\n                </Box>\n              </Box>\n            </Box>\n          )}\n\n          {/* Selezione nuova bobina */}\n          <Autocomplete\n            value={selectedBobina}\n            onChange={(event, newValue) => setSelectedBobina(newValue)}\n            options={bobineCompatibili}\n            getOptionLabel={(option) => `${option.id_bobina} (${option.metri_residui || 0}m residui)`}\n            renderOption={(props, option) => (\n              <Box component=\"li\" {...props}>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>\n                  <BobinaIcon fontSize=\"small\" />\n                  <Box sx={{ flexGrow: 1 }}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                      {option.id_bobina}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {option.tipologia} {option.sezione} - {option.metri_residui || 0}m residui\n                    </Typography>\n                  </Box>\n                  {option.id_bobina === cavo.id_bobina && (\n                    <Chip label=\"Attuale\" size=\"small\" color=\"primary\" />\n                  )}\n                </Box>\n              </Box>\n            )}\n            renderInput={(params) => (\n              <TextField\n                {...params}\n                label=\"Seleziona Bobina\"\n                error={Boolean(error && !selectedBobina)}\n                helperText={`${bobineCompatibili.length} bobine compatibili disponibili`}\n              />\n            )}\n            sx={{ mb: 2 }}\n            disabled={loading}\n          />\n\n          {/* Motivazione */}\n          <TextField\n            label=\"Motivazione della modifica\"\n            multiline\n            rows={3}\n            fullWidth\n            value={motivazione}\n            onChange={(e) => setMotivazione(e.target.value)}\n            error={Boolean(error && !motivazione.trim())}\n            helperText={error || \"Specificare il motivo della modifica (es. bobina danneggiata, ottimizzazione, ecc.)\"}\n            disabled={loading}\n            sx={{ mb: 2 }}\n          />\n\n          {/* Alert per cambio bobina */}\n          {selectedBobina && selectedBobina.id_bobina !== cavo.id_bobina && (\n            <Alert \n              severity=\"info\" \n              icon={<SwapIcon />}\n              sx={{ mt: 2 }}\n            >\n              <Typography variant=\"body2\">\n                <strong>Cambio bobina:</strong> da {cavo.id_bobina || 'N/D'} a {selectedBobina.id_bobina}\n              </Typography>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Questa operazione aggiornerà i metri residui delle bobine coinvolte.\n              </Typography>\n            </Alert>\n          )}\n\n          {/* Alert per bobine incompatibili */}\n          {bobineCompatibili.length === 0 && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Nessuna bobina compatibile disponibile per questo tipo di cavo.\n            </Alert>\n          )}\n        </Box>\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, pb: 3 }}>\n        <Button \n          onClick={handleClose}\n          disabled={loading}\n        >\n          Annulla\n        </Button>\n        <Button \n          onClick={handleSave}\n          variant=\"contained\"\n          disabled={loading || !selectedBobina || !motivazione.trim() || selectedBobina.id_bobina === cavo.id_bobina}\n          startIcon={loading ? null : <SwapIcon />}\n        >\n          {loading ? 'Salvando...' : 'Modifica Bobina'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ModificaBobinaDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,iBAAiB,IAAIC,UAAU,EAC/BC,SAAS,IAAIC,QAAQ,QAChB,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA;AAWA,MAAMC,oBAAoB,GAAGA,CAAC;EAC5BC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,IAAI,GAAG,IAAI;EACXC,iBAAiB,GAAG,EAAE;EACtBC,MAAM,GAAGA,CAAA,KAAM,CAAC,CAAC;EACjBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,IAAI4B,IAAI,IAAIE,IAAI,EAAE;MAChB;MACA,MAAMW,aAAa,GAAGV,iBAAiB,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKd,IAAI,CAACc,SAAS,CAAC;MACjFR,iBAAiB,CAACK,aAAa,IAAI,IAAI,CAAC;MACxCH,cAAc,CAAC,EAAE,CAAC;MAClBE,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC,EAAE,CAACZ,IAAI,EAAEE,IAAI,EAAEC,iBAAiB,CAAC,CAAC;EAEnC,MAAMc,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACV,cAAc,EAAE;MACnBK,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,IAAI,CAACH,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;MACvBN,QAAQ,CAAC,0CAA0C,CAAC;MACpD;IACF;;IAEA;IACA,IAAIL,cAAc,CAACY,SAAS,KAAKjB,IAAI,CAACiB,SAAS,IAAIZ,cAAc,CAACa,OAAO,KAAKlB,IAAI,CAACkB,OAAO,EAAE;MAC1FR,QAAQ,CAAC,iFAAiF,CAAC;MAC3F;IACF;IAEAA,QAAQ,CAAC,EAAE,CAAC;IACZR,MAAM,CAACF,IAAI,CAACmB,OAAO,EAAEd,cAAc,CAACS,SAAS,EAAEP,WAAW,CAAC;EAC7D,CAAC;EAED,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACjB,OAAO,EAAE;MACZO,QAAQ,CAAC,EAAE,CAAC;MACZX,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,IAAI,CAACC,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMqB,iBAAiB,GAAGpB,iBAAiB,CAACqB,MAAM,CAACC,MAAM,IACvDA,MAAM,CAACN,SAAS,KAAKjB,IAAI,CAACiB,SAAS,IAAIM,MAAM,CAACL,OAAO,KAAKlB,IAAI,CAACkB,OACjE,CAAC;EAED,MAAMP,aAAa,GAAGV,iBAAiB,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKd,IAAI,CAACc,SAAS,CAAC;EAEjF,oBACElB,OAAA,CAACzB,MAAM;IACL2B,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEqB,WAAY;IACrBI,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVC,EAAE,EAAE;QACFC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb;IACF,CAAE;IAAAC,QAAA,gBAEFlC,OAAA,CAACxB,WAAW;MAACuD,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,eACzBlC,OAAA,CAACnB,GAAG;QAACkD,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzDlC,OAAA,CAACR,YAAY;UAAC+C,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChC3C,OAAA,CAAClB,UAAU;UAAC8D,OAAO,EAAC,IAAI;UAAAV,QAAA,EAAC;QAEzB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd3C,OAAA,CAACvB,aAAa;MAAAyD,QAAA,eACZlC,OAAA,CAACnB,GAAG;QAACkD,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBAEjBlC,OAAA,CAACf,IAAI;UAAC6D,SAAS;UAACC,OAAO,EAAE,CAAE;UAAChB,EAAE,EAAE;YAAEc,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,eACxClC,OAAA,CAACf,IAAI;YAAC+D,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAf,QAAA,eAChBlC,OAAA,CAACnB,GAAG;cAACkD,EAAE,EAAE;gBACPmB,CAAC,EAAE,CAAC;gBACJC,eAAe,EAAE,SAAS;gBAC1BnB,YAAY,EAAE,CAAC;gBACfoB,MAAM,EAAE,WAAW;gBACnBC,WAAW,EAAE;cACf,CAAE;cAAAnB,QAAA,gBACAlC,OAAA,CAACnB,GAAG;gBAACkD,EAAE,EAAE;kBAAEK,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE,CAAC;kBAAEO,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBAChElC,OAAA,CAACN,SAAS;kBAAC4D,QAAQ,EAAC,OAAO;kBAACf,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C3C,OAAA,CAAClB,UAAU;kBAAC8D,OAAO,EAAC,WAAW;kBAACW,UAAU,EAAC,MAAM;kBAAArB,QAAA,EAC9C9B,IAAI,CAACmB;gBAAO;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACb3C,OAAA,CAAChB,IAAI;kBAACwE,KAAK,EAAC,YAAY;kBAACC,IAAI,EAAC,OAAO;kBAAClB,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACN3C,OAAA,CAAClB,UAAU;gBAAC8D,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,gBAAgB;gBAAAL,QAAA,gBAChDlC,OAAA;kBAAAkC,QAAA,EAAQ;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvC,IAAI,CAACiB,SAAS,EAAC,KAAG,eAAArB,OAAA;kBAAAkC,QAAA,EAAQ;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvC,IAAI,CAACkB,OAAO;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACb3C,OAAA,CAAClB,UAAU;gBAAC8D,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,gBAAgB;gBAAAL,QAAA,gBAChDlC,OAAA;kBAAAkC,QAAA,EAAQ;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvC,IAAI,CAACsD,eAAe,IAAI,CAAC,EAAC,MAAI,eAAA1D,OAAA;kBAAAkC,QAAA,EAAQ;gBAAe;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvC,IAAI,CAACc,SAAS,IAAI,KAAK;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGN5B,aAAa,iBACZf,OAAA,CAACnB,GAAG;UAACkD,EAAE,EAAE;YAAEc,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBACjBlC,OAAA,CAAClB,UAAU;YAAC8D,OAAO,EAAC,WAAW;YAACe,YAAY;YAAAzB,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3C,OAAA,CAACnB,GAAG;YAACkD,EAAE,EAAE;cACPmB,CAAC,EAAE,CAAC;cACJC,eAAe,EAAE,YAAY;cAC7BnB,YAAY,EAAE,CAAC;cACfoB,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE;YACf,CAAE;YAAAnB,QAAA,eACAlC,OAAA,CAACnB,GAAG;cAACkD,EAAE,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACzDlC,OAAA,CAACJ,UAAU;gBAAC0D,QAAQ,EAAC,OAAO;gBAACf,KAAK,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3C,OAAA,CAAClB,UAAU;gBAAC8D,OAAO,EAAC,OAAO;gBAACW,UAAU,EAAC,MAAM;gBAAArB,QAAA,EAC1CnB,aAAa,CAACG;cAAS;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACb3C,OAAA,CAAClB,UAAU;gBAAC8D,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,gBAAgB;gBAAAL,QAAA,GAAC,IAC/C,EAACnB,aAAa,CAAC6C,aAAa,IAAI,CAAC,EAAC,WACtC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD3C,OAAA,CAACd,YAAY;UACX2E,KAAK,EAAEpD,cAAe;UACtBqD,QAAQ,EAAEA,CAACC,KAAK,EAAEC,QAAQ,KAAKtD,iBAAiB,CAACsD,QAAQ,CAAE;UAC3DC,OAAO,EAAExC,iBAAkB;UAC3ByC,cAAc,EAAGC,MAAM,IAAK,GAAGA,MAAM,CAACjD,SAAS,KAAKiD,MAAM,CAACP,aAAa,IAAI,CAAC,YAAa;UAC1FQ,YAAY,EAAEA,CAACC,KAAK,EAAEF,MAAM,kBAC1BnE,OAAA,CAACnB,GAAG;YAACyF,SAAS,EAAC,IAAI;YAAA,GAAKD,KAAK;YAAAnC,QAAA,eAC3BlC,OAAA,CAACnB,GAAG;cAACkD,EAAE,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE,CAAC;gBAAEiC,KAAK,EAAE;cAAO,CAAE;cAAArC,QAAA,gBACxElC,OAAA,CAACJ,UAAU;gBAAC0D,QAAQ,EAAC;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B3C,OAAA,CAACnB,GAAG;gBAACkD,EAAE,EAAE;kBAAEyC,QAAQ,EAAE;gBAAE,CAAE;gBAAAtC,QAAA,gBACvBlC,OAAA,CAAClB,UAAU;kBAAC8D,OAAO,EAAC,OAAO;kBAACW,UAAU,EAAC,MAAM;kBAAArB,QAAA,EAC1CiC,MAAM,CAACjD;gBAAS;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACb3C,OAAA,CAAClB,UAAU;kBAAC8D,OAAO,EAAC,SAAS;kBAACL,KAAK,EAAC,gBAAgB;kBAAAL,QAAA,GACjDiC,MAAM,CAAC9C,SAAS,EAAC,GAAC,EAAC8C,MAAM,CAAC7C,OAAO,EAAC,KAAG,EAAC6C,MAAM,CAACP,aAAa,IAAI,CAAC,EAAC,WACnE;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EACLwB,MAAM,CAACjD,SAAS,KAAKd,IAAI,CAACc,SAAS,iBAClClB,OAAA,CAAChB,IAAI;gBAACwE,KAAK,EAAC,SAAS;gBAACC,IAAI,EAAC,OAAO;gBAAClB,KAAK,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACrD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACL;UACF8B,WAAW,EAAGC,MAAM,iBAClB1E,OAAA,CAACrB,SAAS;YAAA,GACJ+F,MAAM;YACVlB,KAAK,EAAC,kBAAkB;YACxB3C,KAAK,EAAE8D,OAAO,CAAC9D,KAAK,IAAI,CAACJ,cAAc,CAAE;YACzCmE,UAAU,EAAE,GAAGnD,iBAAiB,CAACoD,MAAM;UAAkC;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CACD;UACFZ,EAAE,EAAE;YAAEc,EAAE,EAAE;UAAE,CAAE;UACdiC,QAAQ,EAAEvE;QAAQ;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGF3C,OAAA,CAACrB,SAAS;UACR6E,KAAK,EAAC,4BAA4B;UAClCuB,SAAS;UACTC,IAAI,EAAE,CAAE;UACRnD,SAAS;UACTgC,KAAK,EAAElD,WAAY;UACnBmD,QAAQ,EAAGmB,CAAC,IAAKrE,cAAc,CAACqE,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;UAChDhD,KAAK,EAAE8D,OAAO,CAAC9D,KAAK,IAAI,CAACF,WAAW,CAACS,IAAI,CAAC,CAAC,CAAE;UAC7CwD,UAAU,EAAE/D,KAAK,IAAI,qFAAsF;UAC3GiE,QAAQ,EAAEvE,OAAQ;UAClBwB,EAAE,EAAE;YAAEc,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EAGDlC,cAAc,IAAIA,cAAc,CAACS,SAAS,KAAKd,IAAI,CAACc,SAAS,iBAC5DlB,OAAA,CAACjB,KAAK;UACJoG,QAAQ,EAAC,MAAM;UACfC,IAAI,eAAEpF,OAAA,CAACF,QAAQ;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBZ,EAAE,EAAE;YAAEsD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBAEdlC,OAAA,CAAClB,UAAU;YAAC8D,OAAO,EAAC,OAAO;YAAAV,QAAA,gBACzBlC,OAAA;cAAAkC,QAAA,EAAQ;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,QAAI,EAACvC,IAAI,CAACc,SAAS,IAAI,KAAK,EAAC,KAAG,EAACT,cAAc,CAACS,SAAS;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACb3C,OAAA,CAAClB,UAAU;YAAC8D,OAAO,EAAC,SAAS;YAACL,KAAK,EAAC,gBAAgB;YAAAL,QAAA,EAAC;UAErD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR,EAGAlB,iBAAiB,CAACoD,MAAM,KAAK,CAAC,iBAC7B7E,OAAA,CAACjB,KAAK;UAACoG,QAAQ,EAAC,SAAS;UAACpD,EAAE,EAAE;YAAEsD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,EAAC;QAEzC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB3C,OAAA,CAACtB,aAAa;MAACqD,EAAE,EAAE;QAAEuD,EAAE,EAAE,CAAC;QAAEnD,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAClClC,OAAA,CAACpB,MAAM;QACL2G,OAAO,EAAE/D,WAAY;QACrBsD,QAAQ,EAAEvE,OAAQ;QAAA2B,QAAA,EACnB;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3C,OAAA,CAACpB,MAAM;QACL2G,OAAO,EAAEpE,UAAW;QACpByB,OAAO,EAAC,WAAW;QACnBkC,QAAQ,EAAEvE,OAAO,IAAI,CAACE,cAAc,IAAI,CAACE,WAAW,CAACS,IAAI,CAAC,CAAC,IAAIX,cAAc,CAACS,SAAS,KAAKd,IAAI,CAACc,SAAU;QAC3GsE,SAAS,EAAEjF,OAAO,GAAG,IAAI,gBAAGP,OAAA,CAACF,QAAQ;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAT,QAAA,EAExC3B,OAAO,GAAG,aAAa,GAAG;MAAiB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACnC,EAAA,CAtOIP,oBAAoB;AAAAwF,EAAA,GAApBxF,oBAAoB;AAwO1B,eAAeA,oBAAoB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}