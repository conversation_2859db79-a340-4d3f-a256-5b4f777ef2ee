{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\n\n/**\n * Hook personalizzato per gestire il menu contestuale\n * \n * @returns {Object} Oggetto con stato e funzioni per gestire il menu contestuale\n */\nconst useContextMenu = () => {\n  _s();\n  const [contextMenu, setContextMenu] = useState({\n    open: false,\n    anchorPosition: null,\n    contextData: null\n  });\n\n  // Apre il menu contestuale\n  const openContextMenu = useCallback((event, data = null) => {\n    event.preventDefault();\n    event.stopPropagation();\n    setContextMenu({\n      open: true,\n      anchorPosition: {\n        top: event.clientY,\n        left: event.clientX\n      },\n      contextData: data\n    });\n  }, []);\n\n  // Chiude il menu contestuale\n  const closeContextMenu = useCallback(() => {\n    setContextMenu({\n      open: false,\n      anchorPosition: null,\n      contextData: null\n    });\n  }, []);\n\n  // Gestisce il click destro\n  const handleContextMenu = useCallback((event, data = null) => {\n    openContextMenu(event, data);\n  }, [openContextMenu]);\n  return {\n    contextMenu,\n    openContextMenu,\n    closeContextMenu,\n    handleContextMenu\n  };\n};\n_s(useContextMenu, \"GBlx5uhpgH5SQo6bZMhSFqT5Tn4=\");\nexport default useContextMenu;", "map": {"version": 3, "names": ["useState", "useCallback", "useContextMenu", "_s", "contextMenu", "setContextMenu", "open", "anchorPosition", "contextData", "openContextMenu", "event", "data", "preventDefault", "stopPropagation", "top", "clientY", "left", "clientX", "closeContextMenu", "handleContextMenu"], "sources": ["C:/CMS/webapp/frontend/src/hooks/useContextMenu.js"], "sourcesContent": ["import { useState, useCallback } from 'react';\n\n/**\n * Hook personalizzato per gestire il menu contestuale\n * \n * @returns {Object} Oggetto con stato e funzioni per gestire il menu contestuale\n */\nconst useContextMenu = () => {\n  const [contextMenu, setContextMenu] = useState({\n    open: false,\n    anchorPosition: null,\n    contextData: null\n  });\n\n  // Apre il menu contestuale\n  const openContextMenu = useCallback((event, data = null) => {\n    event.preventDefault();\n    event.stopPropagation();\n\n    setContextMenu({\n      open: true,\n      anchorPosition: {\n        top: event.clientY,\n        left: event.clientX\n      },\n      contextData: data\n    });\n  }, []);\n\n  // Chiude il menu contestuale\n  const closeContextMenu = useCallback(() => {\n    setContextMenu({\n      open: false,\n      anchorPosition: null,\n      contextData: null\n    });\n  }, []);\n\n  // Gestisce il click destro\n  const handleContextMenu = useCallback((event, data = null) => {\n    openContextMenu(event, data);\n  }, [openContextMenu]);\n\n  return {\n    contextMenu,\n    openContextMenu,\n    closeContextMenu,\n    handleContextMenu\n  };\n};\n\nexport default useContextMenu;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;;AAE7C;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGL,QAAQ,CAAC;IAC7CM,IAAI,EAAE,KAAK;IACXC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGR,WAAW,CAAC,CAACS,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;IAC1DD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;IAEvBR,cAAc,CAAC;MACbC,IAAI,EAAE,IAAI;MACVC,cAAc,EAAE;QACdO,GAAG,EAAEJ,KAAK,CAACK,OAAO;QAClBC,IAAI,EAAEN,KAAK,CAACO;MACd,CAAC;MACDT,WAAW,EAAEG;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,gBAAgB,GAAGjB,WAAW,CAAC,MAAM;IACzCI,cAAc,CAAC;MACbC,IAAI,EAAE,KAAK;MACXC,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,iBAAiB,GAAGlB,WAAW,CAAC,CAACS,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;IAC5DF,eAAe,CAACC,KAAK,EAAEC,IAAI,CAAC;EAC9B,CAAC,EAAE,CAACF,eAAe,CAAC,CAAC;EAErB,OAAO;IACLL,WAAW;IACXK,eAAe;IACfS,gBAAgB;IAChBC;EACF,CAAC;AACH,CAAC;AAAChB,EAAA,CA1CID,cAAc;AA4CpB,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}